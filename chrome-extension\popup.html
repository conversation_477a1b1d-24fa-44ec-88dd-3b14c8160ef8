<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 350px;
      padding: 20px;
      font-family: Arial, sans-serif;
    }
    
    .status {
      padding: 10px;
      border-radius: 5px;
      margin-bottom: 15px;
      font-weight: bold;
    }
    
    .connected {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    
    .disconnected {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    
    .message-area {
      margin: 15px 0;
    }
    
    .message-area textarea {
      width: 100%;
      height: 80px;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      resize: vertical;
    }
    
    .button-group {
      margin: 10px 0;
    }
    
    .button-group button {
      padding: 8px 15px;
      margin-right: 10px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
    }
    
    .btn-primary {
      background-color: #007bff;
      color: white;
    }
    
    .btn-success {
      background-color: #28a745;
      color: white;
    }
    
    .btn-secondary {
      background-color: #6c757d;
      color: white;
    }
    
    .messages-log {
      max-height: 200px;
      overflow-y: auto;
      border: 1px solid #ddd;
      padding: 10px;
      background-color: #f8f9fa;
      border-radius: 4px;
      font-family: monospace;
      font-size: 12px;
    }
    
    .message-item {
      margin: 5px 0;
      padding: 5px;
      border-radius: 3px;
    }
    
    .sent {
      background-color: #e3f2fd;
      border-left: 3px solid #2196f3;
    }
    
    .received {
      background-color: #f3e5f5;
      border-left: 3px solid #9c27b0;
    }
  </style>
</head>
<body>
  <h3>Chrome扩展 ↔ Python通信</h3>
  
  <div id="status" class="status disconnected">
    状态: 未连接到Python程序
  </div>
  
  <div class="button-group">
    <button id="connectBtn" class="btn-primary">连接Python</button>
    <button id="clearBtn" class="btn-secondary">清空日志</button>
  </div>
  
  <div class="message-area">
    <label for="messageInput">发送消息到Python:</label>
    <textarea id="messageInput" placeholder="输入要发送的消息...">{"action": "hello", "data": "来自Chrome扩展的问候!"}</textarea>
  </div>
  
  <div class="button-group">
    <button id="sendBtn" class="btn-success">发送消息</button>
  </div>
  
  <div class="message-area">
    <label>消息日志:</label>
    <div id="messagesLog" class="messages-log"></div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
