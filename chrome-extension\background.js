// Chrome扩展后台脚本 - 处理与Python程序的通信

let nativePort = null;

// 连接到Python程序
function connectToNativeApp() {
  console.log('尝试连接到Python程序...');
  
  nativePort = chrome.runtime.connectNative('com.example.python_host');
  
  nativePort.onMessage.addListener((message) => {
    console.log('从Python程序收到消息:', message);
    
    // 将消息转发给popup或content script
    chrome.runtime.sendMessage({
      type: 'FROM_PYTHON',
      data: message
    }).catch(() => {
      // 忽略没有监听器的错误
    });
  });
  
  nativePort.onDisconnect.addListener(() => {
    console.log('与Python程序的连接断开');
    nativePort = null;
    
    // 通知其他组件连接断开
    chrome.runtime.sendMessage({
      type: 'PYTHON_DISCONNECTED'
    }).catch(() => {
      // 忽略没有监听器的错误
    });
  });
}

// 发送消息到Python程序
function sendToPython(message) {
  if (nativePort) {
    console.log('发送消息到Python程序:', message);
    nativePort.postMessage(message);
  } else {
    console.error('未连接到Python程序');
    connectToNativeApp();
  }
}

// 监听来自popup和content script的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('收到内部消息:', message);
  
  switch (message.type) {
    case 'CONNECT_PYTHON':
      connectToNativeApp();
      sendResponse({success: true});
      break;
      
    case 'SEND_TO_PYTHON':
      sendToPython(message.data);
      sendResponse({success: true});
      break;
      
    case 'GET_CONNECTION_STATUS':
      sendResponse({connected: nativePort !== null});
      break;
  }
  
  return true; // 保持消息通道开放
});

// 扩展启动时自动连接
chrome.runtime.onStartup.addListener(() => {
  connectToNativeApp();
});

chrome.runtime.onInstalled.addListener(() => {
  connectToNativeApp();
});
