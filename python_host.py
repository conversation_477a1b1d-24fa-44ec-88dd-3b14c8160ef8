#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Chrome扩展Native Host - Python程序
与Chrome扩展进行双向通信
"""

import sys
import json
import struct
import threading
import time
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('python_host.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class ChromeNativeHost:
    def __init__(self):
        self.running = True
        self.message_count = 0
        
    def read_message(self):
        """从Chrome扩展读取消息"""
        try:
            # 读取消息长度（4字节）
            raw_length = sys.stdin.buffer.read(4)
            if not raw_length or len(raw_length) != 4:
                logging.info("没有更多消息或连接已关闭")
                return None

            message_length = struct.unpack('=I', raw_length)[0]
            logging.info(f"准备读取长度为 {message_length} 的消息")

            if message_length == 0:
                return None

            # 读取消息内容
            message_bytes = sys.stdin.buffer.read(message_length)
            if len(message_bytes) != message_length:
                logging.error(f"消息长度不匹配: 期望 {message_length}, 实际 {len(message_bytes)}")
                return None

            message = message_bytes.decode('utf-8')
            return json.loads(message)

        except EOFError:
            logging.info("输入流已结束")
            return None
        except Exception as e:
            logging.error(f"读取消息时出错: {e}")
            return None
    
    def send_message(self, message):
        """向Chrome扩展发送消息"""
        try:
            # 将消息转换为JSON字符串
            message_json = json.dumps(message, ensure_ascii=False)
            message_bytes = message_json.encode('utf-8')
            
            # 发送消息长度（4字节）
            sys.stdout.buffer.write(struct.pack('=I', len(message_bytes)))
            
            # 发送消息内容
            sys.stdout.buffer.write(message_bytes)
            sys.stdout.buffer.flush()
            
            logging.info(f"发送消息到Chrome: {message}")
            
        except Exception as e:
            logging.error(f"发送消息时出错: {e}")
    
    def process_message(self, message):
        """处理来自Chrome扩展的消息"""
        logging.info(f"收到来自Chrome的消息: {message}")
        
        if not message:
            return
            
        action = message.get('action', '')
        
        # 根据不同的action处理消息
        if action == 'hello':
            # 回复问候消息
            response = {
                'action': 'hello_response',
                'message': f"Python程序收到了你的问候: {message.get('data', '')}",
                'timestamp': datetime.now().isoformat(),
                'python_version': sys.version
            }
            self.send_message(response)
            
        elif action == 'page_info':
            # 处理页面信息
            response = {
                'action': 'page_info_received',
                'message': f"收到页面信息: {message.get('title', 'Unknown')}",
                'url': message.get('url', ''),
                'processed_at': datetime.now().isoformat()
            }
            self.send_message(response)
            
        elif action == 'get_system_info':
            # 返回系统信息
            import platform
            response = {
                'action': 'system_info',
                'data': {
                    'platform': platform.platform(),
                    'python_version': platform.python_version(),
                    'machine': platform.machine(),
                    'processor': platform.processor()
                }
            }
            self.send_message(response)
            
        elif action == 'ping':
            # 简单的ping-pong测试
            response = {
                'action': 'pong',
                'message': 'Python程序正常运行',
                'timestamp': datetime.now().isoformat()
            }
            self.send_message(response)
            
        else:
            # 未知action的默认处理
            response = {
                'action': 'unknown_action',
                'message': f"未知的action: {action}",
                'original_message': message
            }
            self.send_message(response)
    
    def send_periodic_messages(self):
        """定期发送消息到Chrome扩展（演示主动发送）"""
        while self.running:
            time.sleep(30)  # 每30秒发送一次
            if self.running:
                message = {
                    'action': 'periodic_update',
                    'message': f'这是来自Python的定期消息 #{self.message_count}',
                    'timestamp': datetime.now().isoformat(),
                    'count': self.message_count
                }
                self.send_message(message)
                self.message_count += 1
    
    def run(self):
        """主运行循环"""
        logging.info("Python Native Host 启动")

        try:
            # 发送启动消息
            startup_message = {
                'action': 'startup',
                'message': 'Python Native Host 已启动并准备接收消息',
                'timestamp': datetime.now().isoformat()
            }
            self.send_message(startup_message)

            # 启动定期消息发送线程（延迟启动）
            periodic_thread = threading.Thread(target=self.send_periodic_messages)
            periodic_thread.daemon = True
            periodic_thread.start()

            # 主消息处理循环
            while self.running:
                try:
                    message = self.read_message()
                    if message is None:
                        logging.info("没有收到消息，退出循环")
                        break

                    self.process_message(message)

                except Exception as e:
                    logging.error(f"处理消息时出错: {e}")
                    # 不要因为单个消息错误就退出
                    continue

        except KeyboardInterrupt:
            logging.info("收到中断信号，正在关闭...")
        except Exception as e:
            logging.error(f"运行时出错: {e}")
        finally:
            self.running = False
            logging.info("Python Native Host 已关闭")

if __name__ == '__main__':
    host = ChromeNativeHost()
    host.run()
