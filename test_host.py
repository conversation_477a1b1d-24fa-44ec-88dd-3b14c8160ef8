#!/usr/bin/env python3
import sys
import json
import struct
import logging

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(message)s',
    handlers=[logging.FileHandler('test_host.log', mode='w')]
)

def send_message(message):
    try:
        logging.info(f"发送: {message}")
        message_json = json.dumps(message)
        message_bytes = message_json.encode('utf-8')
        length = len(message_bytes)
        
        sys.stdout.buffer.write(struct.pack('=I', length))
        sys.stdout.buffer.write(message_bytes)
        sys.stdout.buffer.flush()
        logging.info("发送成功")
    except Exception as e:
        logging.error(f"发送失败: {e}")

def read_message():
    try:
        raw_length = sys.stdin.buffer.read(4)
        if not raw_length:
            return None
        length = struct.unpack('=I', raw_length)[0]
        message_bytes = sys.stdin.buffer.read(length)
        message = json.loads(message_bytes.decode('utf-8'))
        logging.info(f"收到: {message}")
        return message
    except Exception as e:
        logging.error(f"读取失败: {e}")
        return None

def main():
    logging.info("测试程序启动")
    
    # 发送启动消息
    send_message({"action": "startup", "message": "测试程序已启动"})
    
    # 消息循环
    while True:
        try:
            message = read_message()
            if message:
                # 简单回复
                send_message({"action": "response", "data": "收到消息"})
        except KeyboardInterrupt:
            break
        except Exception as e:
            logging.error(f"错误: {e}")
            break
    
    logging.info("程序结束")

if __name__ == "__main__":
    main()
