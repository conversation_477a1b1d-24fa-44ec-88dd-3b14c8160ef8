#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小化的Chrome Native Host - 用于调试连接问题
"""

import sys
import json
import struct
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('minimal_host.log', encoding='utf-8', mode='w'),
        logging.StreamHandler(sys.stderr)  # 错误输出到stderr
    ]
)

def send_message(message):
    """发送消息到Chrome"""
    try:
        logging.info(f"发送消息: {message}")
        
        # 转换为JSON字符串
        message_json = json.dumps(message, ensure_ascii=False)
        message_bytes = message_json.encode('utf-8')
        
        # 发送消息长度（4字节，小端序）
        length = len(message_bytes)
        sys.stdout.buffer.write(struct.pack('<I', length))
        
        # 发送消息内容
        sys.stdout.buffer.write(message_bytes)
        sys.stdout.buffer.flush()
        
        logging.info(f"消息发送成功，长度: {length}")
        
    except Exception as e:
        logging.error(f"发送消息失败: {e}")

def read_message():
    """读取来自Chrome的消息"""
    try:
        logging.debug("等待读取消息...")
        
        # 读取消息长度（4字节）
        raw_length = sys.stdin.buffer.read(4)
        if not raw_length:
            logging.debug("没有收到长度数据")
            return None
            
        if len(raw_length) != 4:
            logging.error(f"长度数据不完整: {len(raw_length)} 字节")
            return None
            
        # 解包长度（小端序）
        length = struct.unpack('<I', raw_length)[0]
        logging.info(f"消息长度: {length}")
        
        if length == 0:
            return None
            
        # 读取消息内容
        message_bytes = sys.stdin.buffer.read(length)
        if len(message_bytes) != length:
            logging.error(f"消息内容不完整: 期望{length}, 实际{len(message_bytes)}")
            return None
            
        # 解码并解析JSON
        message_str = message_bytes.decode('utf-8')
        message = json.loads(message_str)
        
        logging.info(f"收到消息: {message}")
        return message
        
    except Exception as e:
        logging.error(f"读取消息失败: {e}")
        return None

def main():
    logging.info("最小化Native Host启动")
    
    try:
        # 发送启动消息
        startup_message = {
            "action": "startup",
            "message": "最小化Host已启动",
            "timestamp": datetime.now().isoformat()
        }
        send_message(startup_message)
        
        # 消息处理循环
        message_count = 0
        while True:
            try:
                message = read_message()
                if message is None:
                    logging.debug("没有收到消息，继续等待...")
                    continue
                    
                message_count += 1
                logging.info(f"处理第 {message_count} 条消息")
                
                # 处理不同类型的消息
                action = message.get('action', 'unknown')
                
                if action == 'ping':
                    response = {
                        "action": "pong",
                        "message": "Python程序正常运行",
                        "timestamp": datetime.now().isoformat()
                    }
                elif action == 'hello':
                    response = {
                        "action": "hello_response",
                        "message": f"收到问候: {message.get('data', '')}",
                        "timestamp": datetime.now().isoformat()
                    }
                else:
                    response = {
                        "action": "response",
                        "original_action": action,
                        "message": f"收到消息 #{message_count}",
                        "timestamp": datetime.now().isoformat()
                    }
                
                send_message(response)
                
            except KeyboardInterrupt:
                logging.info("收到中断信号")
                break
            except Exception as e:
                logging.error(f"处理消息时出错: {e}")
                continue
                
    except Exception as e:
        logging.error(f"主程序错误: {e}")
    finally:
        logging.info("最小化Native Host结束")

if __name__ == "__main__":
    main()
