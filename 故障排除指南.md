# Chrome扩展与Python通信故障排除指南

## 错误: "Access to the specified native messaging host is forbidden"

这个错误通常由以下原因引起：

### 1. 🔄 Chrome需要重启

**解决方案：**
1. **完全关闭Chrome浏览器**（确保所有Chrome进程都结束）
2. 重新启动Chrome
3. 重新加载扩展
4. 测试连接

### 2. 📝 扩展ID不匹配

**检查步骤：**
1. 在Chrome中访问 `chrome://extensions/`
2. 找到"Python Communication Extension"
3. 确认扩展ID是：`mhmnmkkcebfnccnbekgigbfkdjppcmpj`
4. 如果不匹配，更新配置文件

### 3. 🔧 注册表配置问题

**验证注册表：**
```bash
python debug_connection.py
```

**手动检查注册表：**
1. 按 `Win + R`，输入 `regedit`
2. 导航到：`HKEY_CURRENT_USER\SOFTWARE\Google\Chrome\NativeMessagingHosts\com.example.python_host`
3. 确认值指向正确的配置文件路径

### 4. 🛡️ 权限问题

**解决方案：**
1. 以管理员权限运行Chrome
2. 确保Python程序有执行权限
3. 检查防火墙设置

### 5. 📂 文件路径问题

**检查文件：**
- ✅ `com.example.python_host.json` 存在
- ✅ `python_host.bat` 存在且可执行
- ✅ `python_host.py` 存在

### 6. 🔍 调试步骤

**1. 测试Python程序：**
```bash
python python_host.py
```
应该看到启动消息。

**2. 检查Chrome扩展控制台：**
1. 右键扩展图标 → "检查弹出内容"
2. 查看Console标签页的错误信息

**3. 检查后台脚本：**
1. 访问 `chrome://extensions/`
2. 点击扩展的"检查视图 service worker"
3. 查看控制台错误

### 7. 🔄 重新安装步骤

如果以上都不行，完全重新安装：

```bash
# 1. 重新运行安装脚本
python setup.py

# 2. 在Chrome中移除扩展
# 3. 重新加载扩展
# 4. 完全重启Chrome
# 5. 测试连接
```

### 8. 🐛 常见错误信息

**"Specified native messaging host not found"**
- 注册表配置错误
- 配置文件路径错误

**"Access to the specified native messaging host is forbidden"**
- 扩展ID不匹配
- Chrome需要重启

**"Could not establish connection"**
- Python程序无法启动
- 路径配置错误

### 9. ✅ 验证成功连接

成功连接后应该看到：
1. 扩展弹窗显示"已连接到Python程序"
2. Python程序日志显示连接信息
3. 可以成功发送和接收消息

### 10. 📞 进一步帮助

如果问题仍然存在：
1. 运行 `python debug_connection.py` 获取详细信息
2. 检查 `python_host.log` 文件
3. 提供Chrome扩展控制台的错误信息
