#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版Chrome Native Host - 用于调试
"""

import sys
import json
import struct
import logging
from datetime import datetime

# 配置日志到文件
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('simple_host.log', encoding='utf-8', mode='w')
    ]
)

def send_message(message):
    """发送消息到Chrome"""
    try:
        logging.info(f"准备发送消息: {message}")
        
        # 转换为JSON
        message_json = json.dumps(message, ensure_ascii=False)
        message_bytes = message_json.encode('utf-8')
        
        # 发送长度
        length = len(message_bytes)
        sys.stdout.buffer.write(struct.pack('=I', length))
        
        # 发送内容
        sys.stdout.buffer.write(message_bytes)
        sys.stdout.buffer.flush()
        
        logging.info(f"消息发送成功，长度: {length}")
        
    except Exception as e:
        logging.error(f"发送消息失败: {e}")

def read_message():
    """读取来自Chrome的消息"""
    try:
        logging.info("等待读取消息...")
        
        # 读取长度
        raw_length = sys.stdin.buffer.read(4)
        if not raw_length or len(raw_length) != 4:
            logging.info("没有收到长度数据")
            return None
            
        length = struct.unpack('=I', raw_length)[0]
        logging.info(f"收到消息长度: {length}")
        
        if length == 0:
            return None
            
        # 读取内容
        message_bytes = sys.stdin.buffer.read(length)
        if len(message_bytes) != length:
            logging.error(f"消息长度不匹配")
            return None
            
        message_str = message_bytes.decode('utf-8')
        message = json.loads(message_str)
        
        logging.info(f"收到消息: {message}")
        return message
        
    except Exception as e:
        logging.error(f"读取消息失败: {e}")
        return None

def main():
    logging.info("简化版Native Host启动")
    
    try:
        # 发送启动消息
        startup_msg = {
            "action": "startup",
            "message": "简化版Host已启动",
            "timestamp": datetime.now().isoformat()
        }
        send_message(startup_msg)
        
        # 消息循环
        message_count = 0
        while True:
            message = read_message()
            if message is None:
                logging.info("没有更多消息，退出")
                break
                
            message_count += 1
            logging.info(f"处理第 {message_count} 条消息")
            
            # 简单回复
            response = {
                "action": "response",
                "original": message,
                "count": message_count,
                "timestamp": datetime.now().isoformat()
            }
            send_message(response)
            
    except Exception as e:
        logging.error(f"主循环错误: {e}")
    finally:
        logging.info("简化版Native Host结束")

if __name__ == "__main__":
    main()
