// Chrome扩展弹窗脚本

document.addEventListener('DOMContentLoaded', function() {
  const statusDiv = document.getElementById('status');
  const connectBtn = document.getElementById('connectBtn');
  const sendBtn = document.getElementById('sendBtn');
  const clearBtn = document.getElementById('clearBtn');
  const messageInput = document.getElementById('messageInput');
  const messagesLog = document.getElementById('messagesLog');
  
  // 更新连接状态显示
  function updateStatus(connected) {
    if (connected) {
      statusDiv.textContent = '状态: 已连接到Python程序';
      statusDiv.className = 'status connected';
      connectBtn.textContent = '重新连接';
      sendBtn.disabled = false;
    } else {
      statusDiv.textContent = '状态: 未连接到Python程序';
      statusDiv.className = 'status disconnected';
      connectBtn.textContent = '连接Python';
      sendBtn.disabled = true;
    }
  }
  
  // 添加消息到日志
  function addMessageToLog(message, type) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message-item ${type}`;
    
    const timestamp = new Date().toLocaleTimeString();
    const typeLabel = type === 'sent' ? '发送' : '接收';
    
    messageDiv.innerHTML = `
      <strong>[${timestamp}] ${typeLabel}:</strong><br>
      ${JSON.stringify(message, null, 2)}
    `;
    
    messagesLog.appendChild(messageDiv);
    messagesLog.scrollTop = messagesLog.scrollHeight;
  }
  
  // 检查连接状态
  function checkConnectionStatus() {
    chrome.runtime.sendMessage({type: 'GET_CONNECTION_STATUS'}, (response) => {
      if (response) {
        updateStatus(response.connected);
      }
    });
  }
  
  // 连接按钮点击事件
  connectBtn.addEventListener('click', function() {
    chrome.runtime.sendMessage({type: 'CONNECT_PYTHON'}, (response) => {
      if (response && response.success) {
        addMessageToLog('尝试连接到Python程序...', 'sent');
        setTimeout(checkConnectionStatus, 1000); // 1秒后检查状态
      }
    });
  });
  
  // 发送消息按钮点击事件
  sendBtn.addEventListener('click', function() {
    const messageText = messageInput.value.trim();
    if (!messageText) {
      alert('请输入要发送的消息');
      return;
    }
    
    try {
      const message = JSON.parse(messageText);
      chrome.runtime.sendMessage({
        type: 'SEND_TO_PYTHON',
        data: message
      }, (response) => {
        if (response && response.success) {
          addMessageToLog(message, 'sent');
        }
      });
    } catch (e) {
      alert('消息格式错误，请输入有效的JSON格式');
    }
  });
  
  // 清空日志按钮点击事件
  clearBtn.addEventListener('click', function() {
    messagesLog.innerHTML = '';
  });
  
  // 监听来自background script的消息
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    switch (message.type) {
      case 'FROM_PYTHON':
        addMessageToLog(message.data, 'received');
        updateStatus(true);
        break;
        
      case 'PYTHON_DISCONNECTED':
        updateStatus(false);
        addMessageToLog('与Python程序的连接已断开', 'received');
        break;
    }
  });
  
  // 页面加载时检查连接状态
  checkConnectionStatus();
});
