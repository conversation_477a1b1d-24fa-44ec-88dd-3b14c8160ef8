# Chrome扩展与Python程序双向通信示例

这个项目演示了如何实现Chrome浏览器扩展与外部Python程序之间的双向通信。

## 项目结构

```
├── chrome-extension/          # Chrome扩展文件
│   ├── manifest.json         # 扩展清单文件
│   ├── background.js         # 后台脚本
│   ├── popup.html           # 弹窗界面
│   ├── popup.js             # 弹窗脚本
│   └── content.js           # 内容脚本
├── python_host.py           # Python Native Host程序
├── com.example.python_host.json  # Native Messaging配置文件
├── setup.py                 # 安装配置脚本
├── test_communication.py    # 通信测试脚本
└── README.md               # 说明文档
```

## 功能特性

### Chrome扩展功能
- 🔗 连接到Python程序
- 📤 发送JSON格式消息到Python
- 📥 接收来自Python的消息
- 📊 实时显示连接状态
- 📝 消息日志记录
- 🌐 页面内容脚本集成

### Python程序功能
- 🔄 双向消息处理
- ⏰ 定期主动发送消息
- 💻 系统信息查询
- 📋 多种消息类型处理
- 📄 详细日志记录

## 安装步骤

### 1. 安装Python Native Host

```bash
# 运行安装脚本
python setup.py
```

### 2. 加载Chrome扩展

1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 `chrome-extension` 文件夹

### 3. 配置扩展ID

1. 复制扩展的ID（在扩展管理页面显示）
2. 编辑 `com.example.python_host.json` 文件
3. 将 `EXTENSION_ID_PLACEHOLDER` 替换为实际的扩展ID
4. 重新运行 `python setup.py`

### 4. 测试通信

```bash
# 基本测试
python test_communication.py

# 交互式测试
python test_communication.py --interactive
```

## 使用方法

### 从Chrome扩展发送消息

1. 点击扩展图标打开弹窗
2. 点击"连接Python"按钮
3. 在文本框中输入JSON格式的消息
4. 点击"发送消息"按钮

示例消息格式：
```json
{
  "action": "hello",
  "data": "来自Chrome的问候!"
}
```

### Python程序处理的消息类型

- `hello`: 问候消息
- `ping`: 连接测试
- `get_system_info`: 获取系统信息
- `page_info`: 页面信息处理

### Python程序主动发送消息

Python程序会：
- 启动时发送欢迎消息
- 每30秒发送定期更新消息
- 响应Chrome扩展的各种请求

## 消息格式

### 标准消息结构
```json
{
  "action": "消息类型",
  "data": "消息数据",
  "timestamp": "时间戳"
}
```

### 示例消息

**Chrome → Python:**
```json
{
  "action": "hello",
  "data": "来自Chrome扩展的问候!"
}
```

**Python → Chrome:**
```json
{
  "action": "hello_response",
  "message": "Python程序收到了你的问候",
  "timestamp": "2024-01-01T12:00:00",
  "python_version": "3.9.0"
}
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查Python程序是否正确安装
   - 验证扩展ID是否正确配置
   - 查看 `python_host.log` 日志文件

2. **消息发送失败**
   - 确认JSON格式正确
   - 检查Python程序是否运行
   - 查看Chrome扩展控制台错误

3. **权限问题**
   - 确保Python脚本有执行权限
   - 检查Native Messaging配置文件路径

### 调试方法

1. **Chrome扩展调试**
   ```
   右键扩展图标 → 检查弹出内容
   ```

2. **Python程序调试**
   ```bash
   # 查看日志
   tail -f python_host.log
   ```

3. **手动测试Native Host**
   ```bash
   python python_host.py
   ```

## 扩展开发

### 添加新的消息类型

1. 在 `python_host.py` 的 `process_message` 方法中添加新的action处理
2. 在Chrome扩展中添加相应的发送逻辑
3. 更新消息格式文档

### 自定义功能

- 修改 `content.js` 实现页面交互
- 扩展 `popup.html` 添加新的UI元素
- 在Python程序中集成其他库和功能

## 技术原理

这个项目使用Chrome的Native Messaging API实现扩展与外部程序的通信：

1. **Chrome扩展** 通过 `chrome.runtime.connectNative()` 连接到Native Host
2. **Native Host** 是一个Python程序，通过stdin/stdout与Chrome通信
3. **消息格式** 使用长度前缀的JSON格式
4. **双向通信** 支持扩展主动发送和Python主动推送

## 许可证

MIT License - 可自由使用和修改。
