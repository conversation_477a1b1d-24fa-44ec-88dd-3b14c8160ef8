# Chrome扩展与Python通信 - 完整测试指南

## 🔧 当前状态

✅ **已修复的问题：**
- 创建了最小化版本的Python程序 (`minimal_host.py`)
- 修复了消息读取逻辑，避免程序立即退出
- 改进了错误处理和日志记录
- 使用正确的字节序（小端序）

✅ **当前配置：**
- 扩展ID: `mhmnmkkcebfnccnbekgigbfkdjppcmpj`
- Python程序: `minimal_host.py`
- 批处理文件: `minimal_host.bat`
- 配置文件: `com.example.python_host.json`

## 🚀 测试步骤

### 步骤1: 手动测试Python程序
```bash
cd "D:\code\测试python和扩展通信"
python minimal_host.py
```

**预期结果：**
- 程序启动并显示启动消息
- 程序等待输入，不会立即退出
- 在 `minimal_host.log` 中看到详细日志

### 步骤2: 完全重启Chrome
1. **关闭所有Chrome窗口**
2. **打开任务管理器，确认所有Chrome进程都已结束**
3. **重新启动Chrome**

### 步骤3: 重新加载扩展
1. 访问 `chrome://extensions/`
2. 找到"Python Communication Extension"
3. 点击刷新按钮🔄

### 步骤4: 测试连接
1. 点击扩展图标
2. 点击"连接Python"按钮
3. 观察连接状态变化

## 📋 预期结果

### ✅ 成功连接时应该看到：
1. **扩展界面：**
   - 状态显示："已连接到Python程序"
   - 连接按钮变为"重新连接"
   - 发送消息按钮变为可用状态

2. **Python程序日志：**
   ```
   最小化Native Host启动
   发送消息: {'action': 'startup', ...}
   消息发送成功，长度: 101
   等待读取消息...
   ```

3. **可以发送测试消息：**
   ```json
   {"action": "ping"}
   ```

### ❌ 如果仍然失败：

**检查日志文件：**
- `minimal_host.log` - Python程序详细日志
- `minimal_host_error.log` - 批处理文件错误输出

**检查Chrome控制台：**
1. 右键扩展图标 → "检查弹出内容"
2. 查看Console标签页的错误信息
3. 查看Network标签页是否有连接尝试

## 🔍 常见问题解决

### 问题1: "Native host has exited"
**原因：** Python程序启动后立即退出
**解决：** 
- 检查 `minimal_host_error.log`
- 确认Python路径正确
- 手动运行 `python minimal_host.py` 测试

### 问题2: "Access to the specified native messaging host is forbidden"
**原因：** 权限或配置问题
**解决：**
- 确认扩展ID正确
- 完全重启Chrome
- 检查注册表配置

### 问题3: 连接建立但无法发送消息
**原因：** 消息格式或编码问题
**解决：**
- 检查JSON格式是否正确
- 查看Python程序日志中的错误信息

## 🛠️ 调试工具

### 1. 检查配置
```bash
python debug_connection.py
```

### 2. 手动测试消息
```bash
# 启动Python程序
python minimal_host.py

# 在另一个终端发送测试消息
echo '{"action":"ping"}' | python -c "
import sys, json, struct
msg = sys.stdin.read().strip()
data = msg.encode('utf-8')
sys.stdout.buffer.write(struct.pack('<I', len(data)))
sys.stdout.buffer.write(data)
sys.stdout.buffer.flush()
"
```

### 3. 查看详细日志
```bash
# 实时查看日志
Get-Content minimal_host.log -Wait
```

## 📞 如果问题仍然存在

请提供以下信息：
1. `minimal_host.log` 的完整内容
2. `minimal_host_error.log` 的内容（如果存在）
3. Chrome扩展控制台的错误信息
4. 手动运行 `python minimal_host.py` 的结果

## 🎯 下一步

一旦连接成功，你可以：
1. 发送各种JSON消息测试功能
2. 切换回完整版本的 `python_host.py`
3. 添加自定义功能和消息处理逻辑
