# 解决 "Native host has exited" 错误

## 🔍 问题分析

"Native host has exited" 错误表示Python程序启动后立即退出了。这通常由以下原因引起：

### 1. 🐍 Python程序启动失败
- Python路径错误
- 脚本路径错误
- 权限问题
- 编码问题

### 2. 📝 配置文件问题
- 路径格式错误
- 中文路径问题
- 权限配置错误

## 🛠️ 解决步骤

### 步骤1: 测试Python程序
```bash
# 直接运行Python程序
cd "D:\code\测试python和扩展通信"
python simple_host.py
```

如果程序正常启动，应该看到启动消息并等待输入。

### 步骤2: 测试批处理文件
```bash
# 运行批处理文件
simple_host.bat
```

### 步骤3: 检查日志文件
查看以下日志文件：
- `simple_host.log` - Python程序日志
- `simple_host_error.log` - 错误日志

### 步骤4: 完全重启Chrome
1. **完全关闭Chrome**（任务管理器确认所有Chrome进程结束）
2. **重新启动Chrome**
3. **重新加载扩展**

### 步骤5: 检查扩展控制台
1. 右键扩展图标 → "检查弹出内容"
2. 查看Console标签页的详细错误信息
3. 点击"连接Python"按钮
4. 观察错误信息

## 🔧 当前配置状态

### 配置文件位置
```
注册表: HKEY_CURRENT_USER\SOFTWARE\Google\Chrome\NativeMessagingHosts\com.example.python_host
配置文件: D:\code\测试python和扩展通信\com.example.python_host.json
```

### 当前配置内容
```json
{
  "name": "com.example.python_host",
  "description": "Chrome扩展与Python程序通信的Native Host",
  "path": "D:\\code\\测试python和扩展通信\\simple_host.bat",
  "type": "stdio",
  "allowed_origins": [
    "chrome-extension://mhmnmkkcebfnccnbekgigbfkdjppcmpj/"
  ]
}
```

## 🚀 测试步骤

### 1. 手动测试
```bash
# 1. 测试Python程序
python simple_host.py

# 2. 在另一个终端发送测试消息
echo '{"action":"test"}' | python -c "
import sys, json, struct
msg = sys.stdin.read().strip()
data = msg.encode('utf-8')
sys.stdout.buffer.write(struct.pack('=I', len(data)))
sys.stdout.buffer.write(data)
"
```

### 2. Chrome扩展测试
1. 打开扩展弹窗
2. 点击"连接Python"
3. 观察连接状态
4. 发送测试消息

## 📋 检查清单

- [ ] Python程序可以直接运行
- [ ] 批处理文件可以正常执行
- [ ] 配置文件路径正确
- [ ] 扩展ID匹配
- [ ] Chrome完全重启
- [ ] 扩展重新加载
- [ ] 没有防火墙阻止

## 🔄 如果仍然失败

### 方案A: 使用绝对Python路径
修改配置文件直接使用Python可执行文件：
```json
{
  "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python.exe D:\\code\\测试python和扩展通信\\simple_host.py"
}
```

### 方案B: 移动到英文路径
将整个项目移动到没有中文字符的路径，如：
```
C:\chrome-python-communication\
```

### 方案C: 使用完整的批处理文件
创建更详细的批处理文件来捕获错误。

## 📞 获取帮助

如果问题仍然存在，请提供：
1. `simple_host.log` 的内容
2. `simple_host_error.log` 的内容
3. Chrome扩展控制台的错误信息
4. 手动运行Python程序的结果
