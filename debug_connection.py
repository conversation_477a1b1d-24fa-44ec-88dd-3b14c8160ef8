#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试Chrome扩展连接问题
"""

import winreg
import json
import os
from pathlib import Path

def check_registry():
    """检查Windows注册表配置"""
    print("检查Windows注册表配置...")
    
    try:
        key_path = r"SOFTWARE\Google\Chrome\NativeMessagingHosts\com.example.python_host"
        key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, key_path)
        config_path, _ = winreg.QueryValueEx(key, "")
        winreg.CloseKey(key)
        
        print(f"✓ 注册表项存在: {key_path}")
        print(f"✓ 配置文件路径: {config_path}")
        
        # 检查配置文件是否存在
        if os.path.exists(config_path):
            print(f"✓ 配置文件存在")
            
            # 读取并验证配置文件
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            print(f"✓ 配置文件内容:")
            print(json.dumps(config, indent=2, ensure_ascii=False))
            
            # 检查Python程序路径
            python_path = config.get('path', '')
            if os.path.exists(python_path):
                print(f"✓ Python程序路径存在: {python_path}")
            else:
                print(f"✗ Python程序路径不存在: {python_path}")
            
            # 检查扩展ID
            allowed_origins = config.get('allowed_origins', [])
            if allowed_origins:
                print(f"✓ 允许的扩展: {allowed_origins}")
            else:
                print(f"✗ 没有配置允许的扩展")
                
        else:
            print(f"✗ 配置文件不存在: {config_path}")
            
    except FileNotFoundError:
        print("✗ 注册表项不存在")
    except Exception as e:
        print(f"✗ 检查注册表时出错: {e}")

def check_chrome_permissions():
    """检查Chrome权限相关信息"""
    print("\n检查Chrome权限...")
    
    # 检查Chrome是否以管理员权限运行
    print("请确保:")
    print("1. Chrome浏览器已完全关闭并重新启动")
    print("2. 扩展已正确加载且ID为: mhmnmkkcebfnccnbekgigbfkdjppcmpj")
    print("3. 扩展具有 'nativeMessaging' 权限")

def main():
    print("Chrome Native Messaging 连接调试")
    print("=" * 40)
    
    check_registry()
    check_chrome_permissions()
    
    print("\n故障排除建议:")
    print("1. 完全关闭Chrome浏览器")
    print("2. 重新启动Chrome")
    print("3. 在扩展中点击'连接Python'按钮")
    print("4. 检查Chrome扩展的控制台是否有错误信息")
    print("5. 如果仍有问题，尝试以管理员权限运行Chrome")

if __name__ == "__main__":
    main()
