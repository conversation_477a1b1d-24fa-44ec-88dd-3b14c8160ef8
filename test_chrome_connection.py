#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Chrome扩展连接的工具
"""

import subprocess
import time
import os
import json

def test_bat_file():
    """测试批处理文件是否能正常运行"""
    print("测试批处理文件...")
    
    try:
        # 启动批处理文件
        process = subprocess.Popen(
            ['simple_host.bat'],
            cwd=r'D:\code\测试python和扩展通信',
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            stdin=subprocess.PIPE
        )
        
        # 等待一秒让程序启动
        time.sleep(1)
        
        # 发送测试消息
        test_message = {
            "action": "test",
            "data": "这是一个测试消息"
        }
        
        message_json = json.dumps(test_message)
        message_bytes = message_json.encode('utf-8')
        
        # 发送长度和消息
        import struct
        length_bytes = struct.pack('=I', len(message_bytes))
        
        process.stdin.write(length_bytes)
        process.stdin.write(message_bytes)
        process.stdin.flush()
        
        # 等待响应
        time.sleep(1)
        
        # 终止进程
        process.terminate()
        
        stdout, stderr = process.communicate(timeout=5)
        
        print(f"返回码: {process.returncode}")
        print(f"标准输出: {stdout}")
        print(f"标准错误: {stderr}")
        
        # 检查日志文件
        if os.path.exists('simple_host.log'):
            with open('simple_host.log', 'r', encoding='utf-8') as f:
                log_content = f.read()
            print(f"日志内容:\n{log_content}")
        
    except Exception as e:
        print(f"测试失败: {e}")

def check_files():
    """检查所有必要文件是否存在"""
    print("检查文件...")
    
    files_to_check = [
        'simple_host.py',
        'simple_host.bat',
        'com.example.python_host.json'
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"✓ {file_path} 存在")
        else:
            print(f"✗ {file_path} 不存在")

def main():
    print("Chrome扩展连接测试工具")
    print("=" * 30)
    
    os.chdir(r'D:\code\测试python和扩展通信')
    
    check_files()
    print()
    test_bat_file()
    
    print("\n测试完成！")
    print("现在请在Chrome扩展中测试连接。")
    print("如果仍有问题，请检查:")
    print("1. Chrome是否完全重启")
    print("2. 扩展是否重新加载")
    print("3. simple_host.log 中的详细日志")

if __name__ == "__main__":
    main()
