#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
温和清空注入 - 温和地清空已有注入，然后依次注入首尾帧
使用方法：
1. 确保Chrome浏览器已开启调试模式（端口9222）
# 方法1：直接命令行启动
chrome.exe --remote-debugging-port=9222 --user-data-dir=chrome-debug


# 方法2：完整路径启动
"C:\Program Files\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 --user-data-dir=chrome-debug

# 方法3：如果Chrome安装在其他位置
"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 --user-data-dir=chrome-debug

🔍 验证调试模式是否开启
方法1：浏览器验证
在Chrome中访问：http://localhost:9222/json

如果显示JSON数据，说明调试模式已开启
如果无法访问，说明调试模式未开启

2. 打开即梦AI页面：https://jimeng.jianying.com/ai-tool/generate?type=video
3. 修改下面的图片路径为您要上传的图片
4. 运行此脚本
"""

import asyncio
import os
import sys
import base64
import mimetypes
import argparse
from pathlib import Path
from playwright.async_api import async_playwright


def read_image_as_base64(image_path: str):
    """读取图片并转换为Base64"""
    try:
        mime_type, _ = mimetypes.guess_type(image_path)
        if not mime_type or not mime_type.startswith('image/'):
            mime_type = 'image/png'
        
        with open(image_path, 'rb') as f:
            image_data = f.read()
        
        base64_data = base64.b64encode(image_data).decode('utf-8')
        
        return {
            "name": os.path.basename(image_path),
            "data": base64_data,
            "type": mime_type,
            "size": len(image_data)
        }
    except Exception as e:
        print(f"❌ 读取图片失败: {e}")
        return None


async def track_generation_task(page):
    """获取生成任务的唯一标识符"""
    try:
        print("📋 获取任务信息...")

        task_info = await page.evaluate("""
            () => {
                console.log('📋 查找任务信息');

                const result = {
                    task_id: null,
                    status: 'unknown',
                    url: window.location.href,
                    timestamp: Date.now()
                };

                // 方法1: 从URL中提取任务ID
                const urlParams = new URLSearchParams(window.location.search);
                const taskId = urlParams.get('taskId') || urlParams.get('id') || urlParams.get('task');
                if (taskId) {
                    result.task_id = taskId;
                    console.log('从URL获取任务ID:', taskId);
                }

                // 方法2: 从页面元素中查找任务ID
                if (!result.task_id) {
                    const taskElements = document.querySelectorAll('[data-task-id], [data-id], [id*="task"]');
                    for (const el of taskElements) {
                        const taskId = el.getAttribute('data-task-id') || el.getAttribute('data-id') || el.id;
                        if (taskId && taskId.length > 5) {
                            result.task_id = taskId;
                            console.log('从页面元素获取任务ID:', taskId);
                            break;
                        }
                    }
                }

                // 方法3: 生成临时任务ID（基于时间戳）
                if (!result.task_id) {
                    result.task_id = 'task_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                    console.log('生成临时任务ID:', result.task_id);
                }

                // 检查页面状态
                const progressElements = document.querySelectorAll('[class*="progress"], [class*="generating"], .loading');
                if (progressElements.length > 0) {
                    result.status = 'generating';
                } else {
                    result.status = 'submitted';
                }

                console.log('任务信息:', result);
                return result;
            }
        """)

        return task_info

    except Exception as e:
        print(f"❌ 获取任务信息失败: {e}")
        return None


async def download_video(page, download_url, task_id):
    """下载生成的视频"""
    try:
        print(f"📥 开始下载视频: {task_id}")
        print(f"🔗 视频URL: {download_url}")

        # 验证URL是否为真实视频
        if any(keyword in download_url.lower() for keyword in ['loading', 'animation', 'progress']):
            print("⚠️ 检测到这可能是加载动画，不是最终视频")
            return False

        # 创建下载目录
        import os
        from datetime import datetime
        import requests

        download_dir = "downloads"
        if not os.path.exists(download_dir):
            os.makedirs(download_dir)

        # 先检查视频文件的实际信息
        try:
            head_response = requests.head(download_url, timeout=10)
            content_type = head_response.headers.get('content-type', '')
            content_length = head_response.headers.get('content-length', '0')

            print(f"📊 文件信息:")
            print(f"   Content-Type: {content_type}")
            print(f"   文件大小: {int(content_length) / 1024 / 1024:.2f} MB" if content_length.isdigit() else "   文件大小: 未知")

            # 检查是否为视频文件
            if not content_type.startswith('video/') and not any(ext in download_url.lower() for ext in ['.mp4', '.mov', '.avi', '.webm']):
                print("⚠️ 文件类型不是视频，可能是错误的链接")
                return False

            # 检查文件大小（如果太小可能不是真实视频）
            if content_length.isdigit() and int(content_length) < 100000:  # 小于100KB
                print("⚠️ 文件太小，可能不是真实视频")
                return False

        except Exception as check_error:
            print(f"⚠️ 无法检查文件信息: {check_error}")

        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 从URL中提取文件扩展名
        file_extension = '.mp4'  # 默认
        if '.' in download_url.split('/')[-1]:
            url_ext = download_url.split('/')[-1].split('.')[-1].lower()
            if url_ext in ['mp4', 'mov', 'avi', 'webm', 'mkv']:
                file_extension = f'.{url_ext}'

        filename = f"jimeng_video_{task_id}_{timestamp}{file_extension}"
        filepath = os.path.join(download_dir, filename)

        print(f"💾 保存路径: {filepath}")

        # 方法1: 使用requests下载（更可靠）
        try:
            print("🔄 使用requests下载...")

            response = requests.get(download_url, stream=True, timeout=60)

            if response.status_code == 200:
                total_size = int(response.headers.get('content-length', 0))
                downloaded_size = 0

                with open(filepath, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            downloaded_size += len(chunk)

                            # 显示下载进度
                            if total_size > 0:
                                progress = (downloaded_size / total_size) * 100
                                print(f"\r📥 下载进度: {progress:.1f}% ({downloaded_size / 1024 / 1024:.2f}MB / {total_size / 1024 / 1024:.2f}MB)", end='')

                print()  # 换行

                # 验证下载的文件
                if os.path.exists(filepath):
                    file_size = os.path.getsize(filepath)
                    print(f"✅ 下载完成！文件大小: {file_size / 1024 / 1024:.2f}MB")

                    # 简单验证是否为视频文件
                    if file_size > 100000:  # 大于100KB
                        print(f"✅ 视频下载成功: {filepath}")
                        return True
                    else:
                        print("⚠️ 下载的文件太小，可能不是完整视频")
                        return False
                else:
                    print("❌ 文件下载失败")
                    return False
            else:
                print(f"❌ 下载失败，HTTP状态码: {response.status_code}")

        except Exception as requests_error:
            print(f"❌ requests下载失败: {requests_error}")

        # 方法2: 使用playwright下载（备用）
        try:
            print("🔄 尝试playwright下载...")

            async with page.expect_download(timeout=60000) as download_info:
                await page.evaluate(f"""
                    () => {{
                        const link = document.createElement('a');
                        link.href = '{download_url}';
                        link.download = '{filename}';
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                    }}
                """)

            download = await download_info.value
            await download.save_as(filepath)

            # 验证下载的文件
            if os.path.exists(filepath):
                file_size = os.path.getsize(filepath)
                print(f"✅ playwright下载成功！文件大小: {file_size / 1024 / 1024:.2f}MB")
                print(f"✅ 视频保存至: {filepath}")
                return True

        except Exception as playwright_error:
            print(f"❌ playwright下载也失败: {playwright_error}")

        return False

    except Exception as e:
        print(f"❌ 下载过程出错: {e}")
        return False


async def monitor_and_download(page, task_info):
    """监控生成进度并下载完成的视频"""
    try:
        task_id = task_info.get('task_id', 'unknown')
        print(f"🔄 开始监控任务: {task_id}")

        # 设置网络请求监听器
        video_urls = []
        progress_data = {}

        def handle_response(response):
            url = response.url

            # 监听视频相关的网络请求
            if any(ext in url.lower() for ext in ['.mp4', '.mov', '.avi', '.webm']):
                if 'record-loading-animation' not in url and 'loading' not in url:
                    print(f"🎬 发现视频URL: {url}")
                    video_urls.append(url)

            # 监听API请求获取进度信息
            if any(api in url.lower() for api in ['api', 'task', 'progress', 'status', 'generate']):
                try:
                    if response.status == 200:
                        print(f"📡 API请求: {url}")
                        # 这里可以进一步解析响应内容获取进度
                except Exception as e:
                    pass

        def handle_request(request):
            url = request.url

            # 监听任务相关的请求
            if any(keyword in url.lower() for keyword in ['task', 'generate', 'progress', 'status']):
                print(f"📤 发送请求: {url}")
                if request.post_data:
                    try:
                        import json
                        post_data = json.loads(request.post_data)
                        if 'taskId' in str(post_data) or 'id' in str(post_data):
                            print(f"📋 请求数据包含任务信息: {post_data}")
                    except:
                        pass

        # 注册网络监听器
        page.on("response", handle_response)
        page.on("request", handle_request)

        max_wait_time = 300  # 最大等待5分钟
        check_interval = 5   # 每5秒检查一次
        elapsed_time = 0

        print("🌐 网络监听器已启动，开始监控...")

        while elapsed_time < max_wait_time:
            print(f"⏱️ 检查进度... ({elapsed_time}s/{max_wait_time}s)")

            # 检查生成状态和进度
            status_info = await page.evaluate("""
                () => {
                    const result = {
                        status: 'unknown',
                        progress: 0,
                        completed: false,
                        error: false,
                        message: '',
                        hasVideo: false,
                        videoSrc: null
                    };

                    // 检查进度相关文本
                    const allText = document.body.textContent || '';
                    const progressMatches = allText.match(/(\\d+)%/g);
                    if (progressMatches) {
                        const percentages = progressMatches.map(p => parseInt(p));
                        result.progress = Math.max(...percentages);
                        if (result.progress > 0 && result.progress < 100) {
                            result.status = 'generating';
                            result.message = `生成进度: ${result.progress}%`;
                        }
                    }

                    // 检查是否有"生成中"、"处理中"等文本
                    const generatingKeywords = ['生成中', '处理中', 'generating', 'processing'];
                    if (generatingKeywords.some(keyword => allText.includes(keyword))) {
                        result.status = 'generating';
                        if (!result.message) result.message = '正在生成中...';
                    }

                    // 检查是否完成
                    const completedKeywords = ['生成完成', '完成', 'completed', 'success', 'done'];
                    if (completedKeywords.some(keyword => allText.includes(keyword))) {
                        result.completed = true;
                        result.status = 'completed';
                    }

                    // 检查错误状态
                    const errorKeywords = ['失败', '错误', 'error', 'failed', 'timeout'];
                    if (errorKeywords.some(keyword => allText.includes(keyword))) {
                        result.error = true;
                        result.status = 'error';
                        result.message = '生成失败';
                    }

                    // 查找视频元素
                    const videos = document.querySelectorAll('video');
                    if (videos.length > 0) {
                        for (const video of videos) {
                            const src = video.src || (video.querySelector('source') && video.querySelector('source').src);
                            if (src && !src.includes('loading') && !src.includes('animation')) {
                                result.hasVideo = true;
                                result.videoSrc = src;
                                result.completed = true;
                                break;
                            }
                        }
                    }

                    // 如果进度达到100%，标记为完成
                    if (result.progress >= 100) {
                        result.completed = true;
                        result.status = 'completed';
                    }

                    console.log('状态检查结果:', result);
                    return result;
                }
            """)

            # 显示进度信息
            if status_info['status'] == 'generating':
                print(f"🔄 生成中... {status_info['progress']}% - {status_info['message']}")
            elif status_info['completed']:
                print("✅ 生成完成！")

                # 优先使用网络监听到的视频URL
                final_video_url = None
                if video_urls:
                    # 选择最可能是最终视频的URL（通常是最后一个，且不包含loading等关键词）
                    for url in reversed(video_urls):
                        if not any(keyword in url.lower() for keyword in ['loading', 'animation', 'progress']):
                            final_video_url = url
                            break

                # 如果网络监听没有找到，使用页面中的视频元素
                if not final_video_url and status_info['videoSrc']:
                    final_video_url = status_info['videoSrc']

                if final_video_url:
                    print(f"🎬 找到最终视频URL: {final_video_url}")

                    # 下载视频
                    download_success = await download_video(page, final_video_url, task_id)

                    # 移除网络监听器
                    page.remove_listener("response", handle_response)
                    page.remove_listener("request", handle_request)

                    return download_success
                else:
                    print("⚠️ 生成完成但未找到视频URL，继续等待...")

            elif status_info['error']:
                print(f"❌ 生成失败: {status_info['message']}")

                # 移除网络监听器
                page.remove_listener("response", handle_response)
                page.remove_listener("request", handle_request)

                return False
            else:
                print(f"⏳ 等待中... 状态: {status_info['status']}")
                if status_info['message']:
                    print(f"   详情: {status_info['message']}")

            # 如果网络监听到了视频URL，也检查一下
            if video_urls:
                print(f"🌐 网络监听到 {len(video_urls)} 个视频URL")
                for i, url in enumerate(video_urls):
                    print(f"   {i+1}. {url}")

            # 等待下次检查
            await asyncio.sleep(check_interval)
            elapsed_time += check_interval

        print("⏰ 监控超时，生成可能仍在进行中")

        # 移除网络监听器
        page.remove_listener("response", handle_response)
        page.remove_listener("request", handle_request)

        # 如果超时但有视频URL，尝试下载
        if video_urls:
            print("🔄 超时但发现视频URL，尝试下载最后一个...")
            final_url = video_urls[-1]
            return await download_video(page, final_url, task_id)

        return False

    except Exception as e:
        print(f"❌ 监控过程出错: {e}")
        return False


async def trigger_manual_download(page, task_id):
    """尝试手动触发下载"""
    try:
        print("🔍 尝试手动触发下载...")

        download_triggered = await page.evaluate("""
            () => {
                console.log('🔍 查找下载触发方式');

                // 查找下载按钮
                const downloadButtons = document.querySelectorAll('button, a, div');
                for (const btn of downloadButtons) {
                    const text = (btn.textContent || '').toLowerCase();
                    const className = (btn.className || '').toLowerCase();

                    if (text.includes('下载') || text.includes('download') ||
                        className.includes('download') || className.includes('save')) {

                        if (btn.offsetParent !== null && !btn.disabled) {
                            console.log('找到下载按钮，点击');
                            btn.click();
                            return true;
                        }
                    }
                }

                // 查找右键菜单选项
                const videos = document.querySelectorAll('video');
                if (videos.length > 0) {
                    const video = videos[0];
                    console.log('找到视频元素，尝试右键下载');

                    // 触发右键菜单
                    const contextMenuEvent = new MouseEvent('contextmenu', {
                        bubbles: true,
                        cancelable: true,
                        view: window
                    });
                    video.dispatchEvent(contextMenuEvent);

                    return true;
                }

                return false;
            }
        """)

        if download_triggered:
            print("✅ 手动下载触发成功")
            await asyncio.sleep(5)  # 等待下载开始
            return True
        else:
            print("⚠️ 无法找到下载方式")
            return False

    except Exception as e:
        print(f"❌ 手动下载触发失败: {e}")
        return False


async def gentle_clear_and_inject(first_frame_path=None, last_frame_path=None):
    """温和清空并注入"""

    # 🔧 配置区域 - 如果没有提供参数，使用默认路径
    if not first_frame_path:
        first_frame_path = r"D:\1、王云领\7.18华创瑞景园\ai生成的图2\保留黑色底背景_保留楼体轮廓_楼体的风格转换成赛博朋克风格.png"
    if not last_frame_path:
        last_frame_path = r"D:\1、王云领\7.18华创瑞景园\ai生成的图2\一座完全由生物发光植物构成的奇幻建筑_孤立在纯黑色背景中_巨大的 (3).png"
    
    print("🌸 温和清空注入工具")
    print("=" * 50)
    print(f"📸 首帧: {Path(first_frame_path).name}")
    print(f"📸 尾帧: {Path(last_frame_path).name}")
    print()
    
    # 验证文件存在
    if not os.path.exists(first_frame_path) or not os.path.exists(last_frame_path):
        print("❌ 图片文件不存在")
        return False
    
    # 读取图片数据
    first_frame_data = read_image_as_base64(first_frame_path)
    last_frame_data = read_image_as_base64(last_frame_path)
    
    if not first_frame_data or not last_frame_data:
        return False
    
    print(f"✅ 首帧数据: {first_frame_data['size']} 字节")
    print(f"✅ 尾帧数据: {last_frame_data['size']} 字节")
    
    # 检查Chrome调试端口
    debug_port = 9222
    import socket
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = sock.connect_ex(('localhost', debug_port))
    sock.close()
    
    if result != 0:
        print("❌ Chrome调试端口未开启")
        return False
    
    print("✅ Chrome调试端口已开启")
    
    async with async_playwright() as p:
        try:
            browser = await p.chromium.connect_over_cdp(f"http://localhost:{debug_port}")
            contexts = browser.contexts
            
            if not contexts:
                return False
            
            # 查找即梦AI页面
            jimeng_page = None
            for context in contexts:
                for page in context.pages:
                    if 'jimeng.jianying.com' in page.url:
                        jimeng_page = page
                        break
                if jimeng_page:
                    break
            
            if not jimeng_page:
                print("❌ 未找到即梦AI页面")
                return False
            
            print(f"✅ 找到即梦AI页面: {jimeng_page.url}")
            
            # 执行温和清空并注入
            success = await perform_gentle_injection(jimeng_page, first_frame_data, last_frame_data)
            
            return success
                
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False


async def perform_gentle_injection(page, first_frame_data, last_frame_data):
    """执行温和注入"""
    try:
        print("🌸 开始温和清空并注入...")
        await asyncio.sleep(2)
        
        # 步骤1: 温和清空 - 只重置文件输入，不删除元素
        print("🧹 步骤1: 温和清空...")
        
        gentle_clear_result = await page.evaluate("""
            () => {
                console.log('🧹 温和清空');
                
                const result = {
                    originalBlobImages: 0,
                    resetInputs: 0,
                    preservedInputs: 0
                };
                
                // 检查当前状态
                result.originalBlobImages = document.querySelectorAll('img[src*="blob:"]').length;
                
                // 温和重置文件输入（不删除元素）
                const fileInputs = document.querySelectorAll('input[type="file"]');
                fileInputs.forEach((input, index) => {
                    try {
                        input.value = '';  // 清空值
                        // 创建空的DataTransfer
                        const emptyTransfer = new DataTransfer();
                        input.files = emptyTransfer.files;
                        result.resetInputs++;
                        console.log(`温和重置文件输入 ${index}`);
                    } catch (e) {
                        console.log(`重置文件输入 ${index} 失败:`, e);
                    }
                });
                
                result.preservedInputs = document.querySelectorAll('input[type="file"]').length;
                
                console.log('温和清空结果:', result);
                return result;
            }
        """)
        
        print(f"🧹 温和清空结果:")
        print(f"   原有预览图片: {gentle_clear_result['originalBlobImages']} 个")
        print(f"   重置输入: {gentle_clear_result['resetInputs']} 个")
        print(f"   保留输入: {gentle_clear_result['preservedInputs']} 个")
        
        await asyncio.sleep(2)
        
        # 步骤2: 如果没有文件输入，尝试刷新页面
        if gentle_clear_result['preservedInputs'] == 0:
            print("🔄 步骤2: 没有文件输入，快速刷新页面...")
            try:
                await page.reload()
                await page.wait_for_load_state('domcontentloaded', timeout=15000)  # 减少超时时间，只等DOM加载
                await asyncio.sleep(2)  # 减少等待时间

                # 重新检查
                new_inputs = await page.evaluate("""
                    () => {
                        return document.querySelectorAll('input[type="file"]').length;
                    }
                """)

                print(f"刷新后文件输入: {new_inputs} 个")

                if new_inputs == 0:
                    print("❌ 刷新后仍无文件输入")
                    return False
            except Exception as e:
                print(f"⚠️ 页面刷新超时，尝试继续: {e}")
                # 即使刷新失败，也尝试继续执行
        
        # 步骤3: 注入JavaScript辅助函数
        print("🔧 步骤3: 注入JavaScript辅助函数...")
        
        await page.evaluate("""
            () => {
                window.createFileFromBase64 = function(base64Data, fileName, mimeType) {
                    try {
                        const byteCharacters = atob(base64Data);
                        const byteNumbers = new Array(byteCharacters.length);
                        for (let i = 0; i < byteCharacters.length; i++) {
                            byteNumbers[i] = byteCharacters.charCodeAt(i);
                        }
                        const byteArray = new Uint8Array(byteNumbers);
                        return new File([byteArray], fileName, { type: mimeType });
                    } catch (error) {
                        console.error('创建文件失败:', error);
                        return null;
                    }
                };
                
                window.setFileToInput = function(input, file) {
                    try {
                        const dataTransfer = new DataTransfer();
                        dataTransfer.items.add(file);
                        input.files = dataTransfer.files;
                        
                        const changeEvent = new Event('change', { bubbles: true });
                        input.dispatchEvent(changeEvent);
                        
                        const inputEvent = new Event('input', { bubbles: true });
                        input.dispatchEvent(inputEvent);
                        
                        return true;
                    } catch (error) {
                        console.error('设置文件失败:', error);
                        return false;
                    }
                };
            }
        """)
        
        print("✅ JavaScript辅助函数注入完成")
        
        # 步骤4: 注入首帧
        print("📸 步骤4: 注入首帧...")
        
        first_result = await page.evaluate(f"""
            () => {{
                try {{
                    const inputs = document.querySelectorAll('input[type="file"]');
                    if (inputs.length === 0) {{
                        console.error('未找到文件输入');
                        return false;
                    }}
                    
                    const input = inputs[0];
                    
                    // 强制显示
                    input.style.display = 'block !important';
                    input.style.visibility = 'visible !important';
                    input.style.opacity = '1 !important';
                    
                    const file = window.createFileFromBase64(
                        '{first_frame_data["data"]}',
                        '{first_frame_data["name"]}',
                        '{first_frame_data["type"]}'
                    );
                    
                    if (!file) return false;
                    
                    return window.setFileToInput(input, file);
                    
                }} catch (error) {{
                    console.error('首帧注入失败:', error);
                    return false;
                }}
            }}
        """)
        
        if not first_result:
            print("❌ 首帧注入失败")
            return False
        
        print("✅ 首帧注入成功")
        await asyncio.sleep(1)  # 减少等待时间

        # 步骤5: 直接注入尾帧（不激活尾帧区域）
        print("📸 步骤5: 直接注入尾帧...")
        
        last_result = await page.evaluate(f"""
            () => {{
                try {{
                    const inputs = document.querySelectorAll('input[type="file"]');
                    console.log('找到文件输入数量:', inputs.length);
                    
                    // 尝试所有文件输入
                    for (let i = 0; i < inputs.length; i++) {{
                        const input = inputs[i];
                        
                        // 强制显示
                        input.style.display = 'block !important';
                        input.style.visibility = 'visible !important';
                        input.style.opacity = '1 !important';
                        
                        const file = window.createFileFromBase64(
                            '{last_frame_data["data"]}',
                            '{last_frame_data["name"]}',
                            '{last_frame_data["type"]}'
                        );
                        
                        if (file && window.setFileToInput(input, file)) {{
                            console.log(`尾帧注入成功到输入 ${{i}}`);
                            return true;
                        }}
                    }}
                    
                    return false;
                    
                }} catch (error) {{
                    console.error('尾帧注入失败:', error);
                    return false;
                }}
            }}
        """)
        
        if last_result:
            print("✅ 尾帧注入成功")
        else:
            print("⚠️ 尾帧注入失败，但首帧已成功")
        
        await asyncio.sleep(3)

        # 步骤7: 检查提示词输入框状态
        print("� 步骤7: 检查提示词输入框状态...")

        prompt_check = await page.evaluate("""
            () => {
                console.log('� 检查提示词输入框状态');

                const result = {
                    textareas: [],
                    inputs: [],
                    promptElements: []
                };

                // 检查所有textarea
                const textareas = document.querySelectorAll('textarea');
                textareas.forEach((textarea, index) => {
                    result.textareas.push({
                        index: index,
                        className: textarea.className || '',
                        placeholder: textarea.placeholder || '',
                        value: textarea.value || '',
                        visible: textarea.offsetParent !== null,
                        id: textarea.id || ''
                    });
                });

                // 检查所有input
                const inputs = document.querySelectorAll('input');
                inputs.forEach((input, index) => {
                    result.inputs.push({
                        index: index,
                        type: input.type || 'text',
                        className: input.className || '',
                        placeholder: input.placeholder || '',
                        value: input.value || '',
                        visible: input.offsetParent !== null,
                        id: input.id || ''
                    });
                });

                // 查找提示词相关元素
                const promptSelectors = [
                    'textarea.prompt-textarea-XfqAoB',
                    'input.prompt-input-ajcKzc',
                    'textarea[placeholder*="首帧图和尾帧图"]',
                    'input[placeholder*="首帧图和尾帧图"]'
                ];

                promptSelectors.forEach(selector => {
                    const element = document.querySelector(selector);
                    if (element) {
                        result.promptElements.push({
                            selector: selector,
                            tagName: element.tagName,
                            className: element.className,
                            placeholder: element.placeholder,
                            value: element.value,
                            visible: element.offsetParent !== null
                        });
                    }
                });

                console.log('提示词输入框检查结果:', result);
                return result;
            }
        """)

        print(f"🔍 提示词输入框检查结果:")
        print(f"   Textarea数量: {len(prompt_check['textareas'])}")
        print(f"   Input数量: {len(prompt_check['inputs'])}")
        print(f"   提示词元素数量: {len(prompt_check['promptElements'])}")

        for i, textarea in enumerate(prompt_check['textareas']):
            if textarea['visible']:
                print(f"   Textarea {i}: {textarea['className'][:50]}... - 可见: {textarea['visible']}")

        for i, input_elem in enumerate(prompt_check['inputs']):
            if input_elem['visible'] and 'prompt' in input_elem['className'].lower():
                print(f"   Input {i}: {input_elem['className'][:50]}... - 可见: {input_elem['visible']}")

        for elem in prompt_check['promptElements']:
            print(f"   提示词元素: {elem['selector']} - {elem['tagName']} - 可见: {elem['visible']}")

        # 步骤8: 注入提示词
        print("💭 步骤8: 注入提示词...")

        prompt_text = "固定镜头，完美过渡效果，电影感调色，炫酷过渡"

        prompt_result = await page.evaluate(f"""
            async () => {{
                console.log('💭 开始真正模拟手动输入提示词');

                try {{
                    let success = false;
                    const promptText = '{prompt_text}';

                    // 首先确保输入框处于展开状态
                    console.log('🔍 检查输入框状态');
                    const mainContent = document.querySelector('.main-content-dP2xNO');
                    const isCollapsed = mainContent && mainContent.classList.contains('collapsed-BEoOVP');
                    console.log('输入框是否折叠:', isCollapsed);

                    // 如果是折叠状态，先展开
                    if (isCollapsed) {{
                        console.log('📖 输入框处于折叠状态，尝试展开');
                        const promptContainer = document.querySelector('.prompt-container-rcKZJC');
                        if (promptContainer) {{
                            promptContainer.click();
                            await new Promise(resolve => setTimeout(resolve, 300));
                        }}
                    }}

                    // 找到textarea进行真正的手动输入模拟
                    const textarea = document.querySelector('textarea.prompt-textarea-XfqAoB');
                    if (textarea) {{
                        console.log('找到textarea，开始真正模拟手动输入');

                        // 确保textarea可见和可编辑
                        textarea.style.display = 'block';
                        textarea.style.visibility = 'visible';
                        textarea.style.height = '88px';

                        // 模拟真实用户点击和聚焦
                        textarea.click();
                        await new Promise(resolve => setTimeout(resolve, 200));

                        textarea.focus();
                        await new Promise(resolve => setTimeout(resolve, 200));

                        // 清空现有内容（模拟Ctrl+A然后删除）
                        textarea.select();
                        document.execCommand('selectAll');
                        document.execCommand('delete');

                        // 使用更真实的输入模拟 - 通过execCommand
                        console.log('开始超真实输入模拟');

                        // 方法1: 使用execCommand插入文本（最接近真实输入）
                        textarea.focus();
                        await new Promise(resolve => setTimeout(resolve, 100));

                        // 清空内容
                        document.execCommand('selectAll');
                        document.execCommand('delete');

                        // 使用execCommand逐段插入文本
                        const chunks = promptText.match(/.{{1,10}}/g) || [promptText]; // 分成10字符的块

                        for (let chunk of chunks) {{
                            // 使用execCommand插入文本块
                            document.execCommand('insertText', false, chunk);
                            await new Promise(resolve => setTimeout(resolve, 50));
                        }}

                        // 方法2: 如果execCommand不支持，使用剪贴板API
                        if (textarea.value !== promptText) {{
                            console.log('execCommand失败，尝试剪贴板方法');

                            try {{
                                // 写入剪贴板
                                await navigator.clipboard.writeText(promptText);

                                // 模拟Ctrl+V粘贴
                                textarea.focus();
                                document.execCommand('selectAll');
                                document.execCommand('delete');

                                const pasteEvent = new ClipboardEvent('paste', {{
                                    clipboardData: new DataTransfer(),
                                    bubbles: true,
                                    cancelable: true
                                }});

                                // 手动设置剪贴板数据
                                Object.defineProperty(pasteEvent, 'clipboardData', {{
                                    value: {{
                                        getData: () => promptText,
                                        setData: () => {{}},
                                        items: [],
                                        files: []
                                    }}
                                }});

                                textarea.dispatchEvent(pasteEvent);

                                // 如果粘贴事件没有自动设置值，手动设置
                                if (textarea.value !== promptText) {{
                                    textarea.value = promptText;
                                }}

                            }} catch (clipboardError) {{
                                console.log('剪贴板方法失败，使用最终方法');
                                textarea.value = promptText;
                            }}
                        }}

                        // 方法3: 触发所有可能的事件来确保状态同步
                        const allEvents = [
                            'input', 'change', 'keyup', 'keydown', 'keypress',
                            'focus', 'blur', 'paste', 'textInput'
                        ];

                        allEvents.forEach(eventType => {{
                            try {{
                                let event;
                                if (eventType === 'input') {{
                                    event = new InputEvent(eventType, {{
                                        bubbles: true,
                                        cancelable: true,
                                        inputType: 'insertText',
                                        data: promptText
                                    }});
                                }} else if (eventType === 'paste') {{
                                    event = new ClipboardEvent(eventType, {{ bubbles: true }});
                                }} else {{
                                    event = new Event(eventType, {{ bubbles: true }});
                                }}
                                textarea.dispatchEvent(event);
                            }} catch (e) {{
                                console.log(`触发事件 ${{eventType}} 失败:`, e);
                            }}
                        }});

                        console.log('超真实输入模拟完成');

                        // 强制建立深层状态绑定
                        console.log('建立深层状态绑定');

                        // 设置自定义属性来标记这是"真实"输入
                        textarea.setAttribute('data-user-input', 'true');
                        textarea.setAttribute('data-prompt-text', promptText);

                        // 创建强制状态保持机制
                        Object.defineProperty(textarea, '_userInputValue', {{
                            value: promptText,
                            writable: false,
                            configurable: false
                        }});

                        // 重写value的getter/setter来保持状态
                        const originalDescriptor = Object.getOwnPropertyDescriptor(HTMLTextAreaElement.prototype, 'value');
                        Object.defineProperty(textarea, 'value', {{
                            get: function() {{
                                const currentValue = originalDescriptor.get.call(this);
                                // 如果值被意外清空，自动恢复
                                if (!currentValue && this.getAttribute('data-user-input') === 'true') {{
                                    const savedValue = this.getAttribute('data-prompt-text');
                                    if (savedValue) {{
                                        originalDescriptor.set.call(this, savedValue);
                                        return savedValue;
                                    }}
                                }}
                                return currentValue;
                            }},
                            set: function(newValue) {{
                                originalDescriptor.set.call(this, newValue);
                                // 如果设置了新值，更新保存的值
                                if (newValue && this.getAttribute('data-user-input') === 'true') {{
                                    this.setAttribute('data-prompt-text', newValue);
                                }}
                            }}
                        }});

                        // 最后触发所有必要事件
                        const finalEvents = ['change', 'blur', 'focusout'];
                        finalEvents.forEach(eventType => {{
                            const event = new Event(eventType, {{ bubbles: true }});
                            textarea.dispatchEvent(event);
                        }});

                        await new Promise(resolve => setTimeout(resolve, 100));

                        console.log('深层状态绑定完成，当前值:', textarea.value);
                        if (textarea.value === promptText) {{
                            success = true;
                        }}
                    }}

                    // 同时处理input元素（使用相同的手动输入模拟）
                    const input = document.querySelector('input.prompt-input-ajcKzc');
                    if (input) {{
                        console.log('同时对input进行手动输入模拟');
                        input.value = '';

                        for (let i = 0; i < promptText.length; i++) {{
                            const char = promptText[i];
                            input.value += char;

                            const inputEvent = new InputEvent('input', {{
                                bubbles: true,
                                cancelable: true,
                                inputType: 'insertText',
                                data: char
                            }});
                            input.dispatchEvent(inputEvent);
                        }}

                        const changeEvent = new Event('change', {{ bubbles: true }});
                        input.dispatchEvent(changeEvent);
                    }}

                    console.log('真正手动输入模拟结果:', success ? '成功' : '失败');
                    return success;

                }} catch (error) {{
                    console.error('手动输入模拟异常:', error);
                    return false;
                }}
            }}
        """)

        if prompt_result:
            print(f"✅ 提示词注入成功: {prompt_text}")
        else:
            print("⚠️ 提示词注入失败，可能需要手动输入")

        await asyncio.sleep(2)

        # 步骤8.1: 验证提示词注入结果
        print("🔍 步骤8.1: 验证提示词注入结果...")

        prompt_verify = await page.evaluate("""
            () => {
                console.log('🔍 验证提示词注入结果');

                const result = {
                    textareaValue: '',
                    inputValue: '',
                    foundPrompt: false
                };

                // 检查textarea
                const textarea = document.querySelector('textarea.prompt-textarea-XfqAoB');
                if (textarea) {
                    result.textareaValue = textarea.value || '';
                    console.log('Textarea当前值:', result.textareaValue);
                }

                // 检查input
                const input = document.querySelector('input.prompt-input-ajcKzc');
                if (input) {
                    result.inputValue = input.value || '';
                    console.log('Input当前值:', result.inputValue);
                }

                // 检查是否有提示词内容
                if (result.textareaValue.includes('固定镜头') || result.inputValue.includes('固定镜头')) {
                    result.foundPrompt = true;
                }

                console.log('提示词验证结果:', result);
                return result;
            }
        """)

        print(f"🔍 提示词验证结果:")
        if prompt_verify['textareaValue']:
            print(f"   Textarea值: {prompt_verify['textareaValue']}")
        if prompt_verify['inputValue']:
            print(f"   Input值: {prompt_verify['inputValue']}")
        print(f"   找到提示词: {'✅' if prompt_verify['foundPrompt'] else '❌'}")

        await asyncio.sleep(1)  # 减少等待时间

        # 跳过折叠测试，直接进入时长设置
        print("⏭️ 跳过折叠测试，加速进入时长设置...")

        # 快速验证提示词稳定性
        final_check = await page.evaluate("""
            () => {
                const textarea = document.querySelector('textarea.prompt-textarea-XfqAoB');
                return {
                    stable: textarea && textarea.value.includes('固定镜头')
                };
            }
        """)

        print(f"✅ 提示词稳定: {'是' if final_check['stable'] else '否'}")

        # 步骤8.3: 时长设置前的页面元素检查
        print("🔍 步骤8.3: 时长设置前的页面元素检查...")

        before_duration_check = await page.evaluate("""
            () => {
                console.log('🔍 时长设置前的页面元素检查');

                const result = {
                    promptElements: [],
                    durationElements: [],
                    promptValues: {}
                };

                // 检查提示词元素
                const textarea = document.querySelector('textarea.prompt-textarea-XfqAoB');
                const input = document.querySelector('input.prompt-input-ajcKzc');

                if (textarea) {
                    result.promptElements.push({
                        type: 'textarea',
                        value: textarea.value || '',
                        visible: textarea.offsetParent !== null,
                        className: textarea.className
                    });
                    result.promptValues.textarea = textarea.value || '';
                }

                if (input) {
                    result.promptElements.push({
                        type: 'input',
                        value: input.value || '',
                        visible: input.offsetParent !== null,
                        className: input.className
                    });
                    result.promptValues.input = input.value || '';
                }

                // 检查时长选择器
                const allSelects = document.querySelectorAll('.lv-select, [role="combobox"]');
                allSelects.forEach((select, index) => {
                    const valueSpan = select.querySelector('.lv-select-view-value');
                    if (valueSpan) {
                        const currentValue = valueSpan.textContent.trim();
                        result.durationElements.push({
                            index: index,
                            value: currentValue,
                            className: select.className,
                            visible: select.offsetParent !== null
                        });
                    }
                });

                console.log('时长设置前检查结果:', result);
                return result;
            }
        """)

        print(f"🔍 时长设置前检查结果:")
        print(f"   提示词元素数量: {len(before_duration_check['promptElements'])}")
        for elem in before_duration_check['promptElements']:
            print(f"   {elem['type']}: '{elem['value'][:30]}...' - 可见: {elem['visible']}")

        print(f"   时长选择器数量: {len(before_duration_check['durationElements'])}")
        for elem in before_duration_check['durationElements']:
            print(f"   选择器 {elem['index']}: '{elem['value']}' - 可见: {elem['visible']}")

        # 步骤9: 设置视频时长为10秒
        print("⏱️ 步骤9: 设置视频时长为10秒...")

        # 首先检查当前时长选择器状态
        duration_check = await page.evaluate("""
            () => {
                console.log('⏱️ 检查时长选择器状态');

                const result = {
                    currentDuration: '',
                    selectElements: [],
                    durationOptions: []
                };

                // 查找时长选择器
                const selectElements = document.querySelectorAll('.lv-select, [role="combobox"]');
                selectElements.forEach((select, index) => {
                    const valueSpan = select.querySelector('.lv-select-view-value');
                    const currentValue = valueSpan ? valueSpan.textContent.trim() : '';

                    result.selectElements.push({
                        index: index,
                        className: select.className,
                        currentValue: currentValue,
                        visible: select.offsetParent !== null
                    });

                    if (currentValue.includes('s') || currentValue.includes('秒')) {
                        result.currentDuration = currentValue;
                    }
                });

                console.log('时长选择器检查结果:', result);
                return result;
            }
        """)

        print(f"⏱️ 时长选择器检查结果:")
        print(f"   当前时长: {duration_check['currentDuration']}")
        print(f"   选择器数量: {len(duration_check['selectElements'])}")

        for i, select in enumerate(duration_check['selectElements']):
            if select['visible']:
                print(f"   选择器 {i}: {select['currentValue']} - 可见: {select['visible']}")

        duration_result = await page.evaluate("""
            () => {
                console.log('⏱️ 开始精确设置视频时长为10秒');

                try {
                    // 精确查找显示"5s"的时长选择器
                    let durationSelect = null;
                    const allSelects = document.querySelectorAll('.lv-select, [role="combobox"]');

                    for (const select of allSelects) {
                        const valueSpan = select.querySelector('.lv-select-view-value');
                        if (valueSpan) {
                            const currentValue = valueSpan.textContent.trim();
                            console.log('检查选择器:', currentValue);

                            if (currentValue === '5s' || currentValue.includes('5s')) {
                                durationSelect = select;
                                console.log('找到时长选择器，当前值:', currentValue);
                                break;
                            }
                        }
                    }

                    if (!durationSelect) {
                        console.error('未找到显示5s的时长选择器');
                        return false;
                    }

                    // 点击选择器打开下拉菜单
                    durationSelect.click();
                    console.log('点击了时长选择器');

                    // 等待下拉菜单出现并查找10秒选项
                    return new Promise((resolve) => {
                        setTimeout(() => {
                            console.log('开始查找10秒选项');

                            // 查找下拉菜单选项
                            const options = document.querySelectorAll('[role="option"], .lv-select-option, .select-option, .lv-select-popup-option');
                            let found10s = false;

                            console.log('找到下拉选项数量:', options.length);

                            for (const option of options) {
                                const text = (option.textContent || '').trim();
                                console.log('检查选项:', text);

                                if (text === '10s' || text === '10秒') {
                                    console.log('找到10秒选项，点击');
                                    option.click();
                                    found10s = true;
                                    break;
                                }
                            }

                            // 如果没找到选项，尝试查找弹出菜单中的所有元素
                            if (!found10s) {
                                console.log('在下拉选项中未找到，搜索弹出菜单');

                                const popupElements = document.querySelectorAll('.lv-select-popup *, [class*="popup"] *, [class*="dropdown"] *');
                                for (const el of popupElements) {
                                    const text = (el.textContent || '').trim();
                                    if (text === '10s' || text === '10秒') {
                                        console.log('在弹出菜单中找到10秒选项:', text);
                                        el.click();
                                        found10s = true;
                                        break;
                                    }
                                }
                            }

                            // 最后尝试：查找所有可见的包含10s的元素
                            if (!found10s) {
                                console.log('最后尝试：全局搜索10s元素');

                                const allElements = document.querySelectorAll('*');
                                for (const el of allElements) {
                                    const text = (el.textContent || '').trim();
                                    if (text === '10s' && el.offsetParent !== null) {
                                        const rect = el.getBoundingClientRect();
                                        if (rect.width > 0 && rect.height > 0) {
                                            console.log('全局搜索找到10s元素，点击');
                                            el.click();
                                            found10s = true;
                                            break;
                                        }
                                    }
                                }
                            }

                            console.log('10秒选项查找结果:', found10s);
                            resolve(found10s);
                        }, 300); // 减少等待时间
                    });

                } catch (error) {
                    console.error('精确设置时长失败:', error);
                    return false;
                }
            }
        """)

        if duration_result:
            print("✅ 视频时长设置为10秒成功")
        else:
            print("⚠️ 视频时长设置失败，可能需要手动选择10秒")

        await asyncio.sleep(1)  # 减少等待时间

        # 步骤9.1: 时长设置后的页面元素检查
        print("🔍 步骤9.1: 时长设置后的页面元素检查...")

        after_duration_check = await page.evaluate("""
            () => {
                console.log('🔍 时长设置后的页面元素检查');

                const result = {
                    promptElements: [],
                    durationElements: [],
                    promptValues: {},
                    promptLost: false
                };

                // 检查提示词元素
                const textarea = document.querySelector('textarea.prompt-textarea-XfqAoB');
                const input = document.querySelector('input.prompt-input-ajcKzc');

                if (textarea) {
                    result.promptElements.push({
                        type: 'textarea',
                        value: textarea.value || '',
                        visible: textarea.offsetParent !== null,
                        className: textarea.className
                    });
                    result.promptValues.textarea = textarea.value || '';
                }

                if (input) {
                    result.promptElements.push({
                        type: 'input',
                        value: input.value || '',
                        visible: input.offsetParent !== null,
                        className: input.className
                    });
                    result.promptValues.input = input.value || '';
                }

                // 检查提示词是否丢失
                const hasPrompt = (result.promptValues.textarea && result.promptValues.textarea.includes('固定镜头')) ||
                                 (result.promptValues.input && result.promptValues.input.includes('固定镜头'));
                result.promptLost = !hasPrompt;

                // 检查时长选择器
                const allSelects = document.querySelectorAll('.lv-select, [role="combobox"]');
                allSelects.forEach((select, index) => {
                    const valueSpan = select.querySelector('.lv-select-view-value');
                    if (valueSpan) {
                        const currentValue = valueSpan.textContent.trim();
                        result.durationElements.push({
                            index: index,
                            value: currentValue,
                            className: select.className,
                            visible: select.offsetParent !== null
                        });
                    }
                });

                console.log('时长设置后检查结果:', result);
                return result;
            }
        """)

        print(f"🔍 时长设置后检查结果:")
        print(f"   提示词元素数量: {len(after_duration_check['promptElements'])}")
        for elem in after_duration_check['promptElements']:
            print(f"   {elem['type']}: '{elem['value'][:30]}...' - 可见: {elem['visible']}")

        print(f"   提示词丢失: {'❌ 是' if after_duration_check['promptLost'] else '✅ 否'}")

        print(f"   时长选择器数量: {len(after_duration_check['durationElements'])}")
        for elem in after_duration_check['durationElements']:
            print(f"   选择器 {elem['index']}: '{elem['value']}' - 可见: {elem['visible']}")

        # 如果提示词丢失，重新注入
        if after_duration_check['promptLost']:
            print("🔄 检测到提示词丢失，重新注入...")

            prompt_text = "固定镜头，完美过渡效果，电影感调色，炫酷过渡"

            reinject_result = await page.evaluate(f"""
                async () => {{
                    console.log('🔄 重新注入提示词');

                    const promptText = '{prompt_text}';
                    let success = false;

                    // 重新注入到textarea
                    const textarea = document.querySelector('textarea.prompt-textarea-XfqAoB');
                    if (textarea) {{
                        // 使用更强的注入方法
                        textarea.click();
                        await new Promise(resolve => setTimeout(resolve, 100));

                        textarea.focus();
                        await new Promise(resolve => setTimeout(resolve, 100));

                        // 清空并重新输入
                        textarea.value = '';
                        textarea.value = promptText;

                        // 触发多种事件
                        const events = ['input', 'change', 'keyup', 'blur'];
                        events.forEach(eventType => {{
                            const event = new Event(eventType, {{ bubbles: true }});
                            textarea.dispatchEvent(event);
                        }});

                        // 特殊的InputEvent
                        const inputEvent = new InputEvent('input', {{
                            bubbles: true,
                            cancelable: true,
                            inputType: 'insertText',
                            data: promptText
                        }});
                        textarea.dispatchEvent(inputEvent);

                        if (textarea.value === promptText) {{
                            success = true;
                        }}
                    }}

                    // 同时处理input
                    const input = document.querySelector('input.prompt-input-ajcKzc');
                    if (input) {{
                        input.value = promptText;
                        const inputEvent = new InputEvent('input', {{ bubbles: true }});
                        input.dispatchEvent(inputEvent);
                    }}

                    console.log('重新注入结果:', success);
                    return success;
                }}
            """)

            print(f"🔄 重新注入结果: {'✅ 成功' if reinject_result else '❌ 失败'}")
            await asyncio.sleep(2)

        # 步骤9.2: 验证时长设置结果
        print("🔍 步骤9.2: 验证时长设置结果...")

        duration_verify = await page.evaluate("""
            () => {
                console.log('🔍 验证时长设置结果');

                const result = {
                    currentDuration: '',
                    is10seconds: false,
                    selectValue: '',
                    allSelectors: []
                };

                // 检查所有选择器，找到时长相关的
                const allSelects = document.querySelectorAll('.lv-select, [role="combobox"]');

                for (const select of allSelects) {
                    const valueSpan = select.querySelector('.lv-select-view-value');
                    if (valueSpan) {
                        const currentValue = valueSpan.textContent.trim();
                        result.allSelectors.push(currentValue);

                        // 如果包含秒数，这就是时长选择器
                        if (currentValue.includes('s') || currentValue.includes('秒')) {
                            result.selectValue = currentValue;
                            result.currentDuration = currentValue;

                            if (currentValue.includes('10')) {
                                result.is10seconds = true;
                            }

                            console.log('找到时长选择器:', currentValue);
                        }
                    }
                }

                console.log('时长验证结果:', result);
                return result;
            }
        """)

        print(f"🔍 时长验证结果:")
        print(f"   当前显示时长: {duration_verify['currentDuration']}")
        print(f"   是否为10秒: {'✅' if duration_verify['is10seconds'] else '❌'}")

        # 如果还不是10秒，尝试其他方法
        if not duration_verify['is10seconds']:
            print("🔄 时长仍不是10秒，尝试其他设置方法...")

            alternative_duration = await page.evaluate("""
                () => {
                    console.log('🔄 尝试其他时长设置方法');

                    try {
                        // 方法1: 直接查找并点击包含10的元素
                        const allElements = document.querySelectorAll('*');
                        let found = false;

                        for (const el of allElements) {
                            const text = (el.textContent || '').trim();

                            // 查找可能的10秒选项
                            if ((text === '10s' || text === '10秒' || text === '10') &&
                                el.offsetParent !== null &&
                                el.getBoundingClientRect().width > 10 &&
                                el.getBoundingClientRect().height > 10) {

                                console.log('找到可能的10秒选项:', text, el.tagName, el.className);

                                // 检查是否是可点击的选项
                                if (el.tagName === 'DIV' || el.tagName === 'SPAN' ||
                                    el.tagName === 'BUTTON' || el.role === 'option') {

                                    el.click();
                                    console.log('点击了10秒选项');
                                    found = true;
                                    break;
                                }
                            }
                        }

                        // 方法2: 尝试键盘操作
                        if (!found) {
                            console.log('尝试键盘操作');
                            const durationSelect = document.querySelector('.lv-select.select-RZ70Y2, [role="combobox"]');
                            if (durationSelect) {
                                durationSelect.focus();

                                // 模拟按下向下箭头键
                                const downEvent = new KeyboardEvent('keydown', {
                                    key: 'ArrowDown',
                                    code: 'ArrowDown',
                                    keyCode: 40,
                                    bubbles: true
                                });
                                durationSelect.dispatchEvent(downEvent);

                                setTimeout(() => {
                                    // 模拟按Enter选择
                                    const enterEvent = new KeyboardEvent('keydown', {
                                        key: 'Enter',
                                        code: 'Enter',
                                        keyCode: 13,
                                        bubbles: true
                                    });
                                    durationSelect.dispatchEvent(enterEvent);
                                }, 200);

                                found = true;
                            }
                        }

                        return found;

                    } catch (error) {
                        console.error('其他时长设置方法失败:', error);
                        return false;
                    }
                }
            """)

            print(f"🔄 其他设置方法结果: {'✅ 尝试成功' if alternative_duration else '❌ 仍然失败'}")

            await asyncio.sleep(2)

            # 再次验证
            final_duration_check = await page.evaluate("""
                () => {
                    // 查找所有选择器，找到时长相关的
                    const allSelects = document.querySelectorAll('.lv-select, [role="combobox"]');

                    for (const select of allSelects) {
                        const valueSpan = select.querySelector('.lv-select-view-value');
                        if (valueSpan) {
                            const currentValue = valueSpan.textContent.trim();

                            // 如果包含秒数，这就是时长选择器
                            if (currentValue.includes('s') || currentValue.includes('秒')) {
                                return {
                                    currentDuration: currentValue,
                                    is10seconds: currentValue.includes('10')
                                };
                            }
                        }
                    }

                    return { currentDuration: '', is10seconds: false };
                }
            """)

            print(f"🔍 最终时长检查:")
            print(f"   最终时长: {final_duration_check['currentDuration']}")
            print(f"   是否为10秒: {'✅' if final_duration_check['is10seconds'] else '❌ 需要手动设置'}")

        await asyncio.sleep(2)

        # 步骤10: 点击提交按钮并开始任务跟踪
        print("� 步骤10: 点击提交按钮并开始任务跟踪...")

        submit_result = await page.evaluate("""
            () => {
                console.log('🚀 查找并点击提交按钮');

                const result = {
                    buttonFound: false,
                    buttonClicked: false,
                    taskId: null,
                    error: null
                };

                try {
                    // 查找提交按钮
                    const submitButton = document.querySelector('button.submit-button-VW0U_J, button[class*="submit-button"]');

                    if (submitButton && submitButton.offsetParent !== null && !submitButton.disabled) {
                        result.buttonFound = true;
                        console.log('找到提交按钮，准备点击');

                        // 点击提交按钮
                        submitButton.click();
                        result.buttonClicked = true;
                        console.log('✅ 提交按钮已点击');

                        return result;
                    } else {
                        // 备用方法：查找所有可能的提交按钮
                        const allButtons = document.querySelectorAll('button');
                        for (const btn of allButtons) {
                            const className = btn.className || '';
                            const hasSubmitClass = className.includes('submit') || className.includes('button-wtoV7J');
                            const hasIcon = btn.querySelector('svg');

                            if (hasSubmitClass && hasIcon && btn.offsetParent !== null && !btn.disabled) {
                                result.buttonFound = true;
                                btn.click();
                                result.buttonClicked = true;
                                console.log('✅ 通过备用方法点击了提交按钮');
                                break;
                            }
                        }
                    }

                } catch (error) {
                    result.error = error.message;
                    console.error('点击提交按钮失败:', error);
                }

                return result;
            }
        """)

        if submit_result['buttonClicked']:
            print("✅ 提交按钮点击成功，视频生成已开始")

            # 等待页面响应
            await asyncio.sleep(3)

            # 获取任务ID和开始跟踪
            task_info = await track_generation_task(page)

            if task_info:
                print(f"📋 任务ID: {task_info.get('task_id', 'Unknown')}")
                print("🔄 开始实时跟踪生成进度...")

                # 实时跟踪生成进度
                download_success = await monitor_and_download(page, task_info)

                return download_success
            else:
                print("⚠️ 无法获取任务信息，但提交已成功")
                return True

        else:
            print("❌ 提交按钮点击失败")
            if submit_result['error']:
                print(f"   错误: {submit_result['error']}")

            # 显示最终验证信息
            final_state = await page.evaluate("""
                () => {
                    return {
                        blobImages: document.querySelectorAll('img[src*="blob:"]').length,
                        promptValue: document.querySelector('textarea.prompt-textarea-XfqAoB')?.value || ''
                    };
                }
            """)

            print("\n🎯 设置完成总结:")
            print(f"   ✅ 首尾帧: {'2张图片' if final_state['blobImages'] >= 2 else '1张图片' if final_state['blobImages'] == 1 else '失败'}")
            print(f"   ✅ 提示词: {'已设置' if final_state['promptValue'] else '需要手动输入'}")
            print(f"   ✅ 时长: 10秒")
            print("   💡 请手动点击提交按钮")

            return final_state['blobImages'] >= 1
        
    except Exception as e:
        print(f"❌ 温和注入失败: {e}")
        return False


async def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='温和清空注入工具')
    parser.add_argument('--first-image', help='首帧图片路径')
    parser.add_argument('--last-image', help='末帧图片路径')
    parser.add_argument('--output', help='输出视频路径')

    args = parser.parse_args()

    # 如果提供了命令行参数，使用参数中的图片路径
    if args.first_image and args.last_image:
        print(f"🎯 使用命令行参数:")
        print(f"   首帧: {args.first_image}")
        print(f"   末帧: {args.last_image}")
        if args.output:
            print(f"   输出: {args.output}")

        success = await gentle_clear_and_inject(args.first_image, args.last_image)
    else:
        success = await gentle_clear_and_inject()

    if success:
        print("\n🎉 完整注入成功！")
        print("💡 请在Chrome浏览器中查看结果")
        print("🚀 如果一切正常，现在可以点击生成按钮创建10秒视频")
    else:
        print("\n❌ 注入失败")
        print("💡 请检查页面状态或手动完成剩余步骤")


if __name__ == "__main__":
    print("=" * 60)
    print("🌸 完整视频生成注入工具")
    print("=" * 60)
    print("🎯 功能：")
    print("   1. 温和清空已有注入")
    print("   2. 依次注入首帧和尾帧")
    print("   3. 注入提示词：固定镜头，完美过渡效果，电影感调色，炫酷过渡")
    print("   4. 设置视频时长为10秒")
    print("   5. 准备生成视频")
    print("=" * 60)
    print()

    asyncio.run(main())
