#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Chrome扩展与Python程序通信
"""

import json
import struct
import sys
import threading
import time

def send_test_message(message):
    """发送测试消息到Python Host"""
    try:
        # 将消息转换为JSON字符串
        message_json = json.dumps(message, ensure_ascii=False)
        message_bytes = message_json.encode('utf-8')
        
        # 发送消息长度（4字节）
        sys.stdout.buffer.write(struct.pack('=I', len(message_bytes)))
        
        # 发送消息内容
        sys.stdout.buffer.write(message_bytes)
        sys.stdout.buffer.flush()
        
        print(f"发送测试消息: {message}")
        
    except Exception as e:
        print(f"发送消息时出错: {e}")

def read_response():
    """读取Python Host的响应"""
    try:
        # 读取消息长度（4字节）
        raw_length = sys.stdin.buffer.read(4)
        if not raw_length:
            return None
            
        message_length = struct.unpack('=I', raw_length)[0]
        
        # 读取消息内容
        message = sys.stdin.buffer.read(message_length).decode('utf-8')
        return json.loads(message)
        
    except Exception as e:
        print(f"读取响应时出错: {e}")
        return None

def test_basic_communication():
    """测试基本通信功能"""
    print("测试Chrome扩展与Python程序通信")
    print("=" * 40)
    
    # 测试消息列表
    test_messages = [
        {
            "action": "hello",
            "data": "这是一个测试消息"
        },
        {
            "action": "ping"
        },
        {
            "action": "get_system_info"
        },
        {
            "action": "page_info",
            "url": "https://example.com",
            "title": "测试页面"
        }
    ]
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n测试 {i}: {message['action']}")
        send_test_message(message)
        
        # 等待响应
        response = read_response()
        if response:
            print(f"收到响应: {json.dumps(response, indent=2, ensure_ascii=False)}")
        else:
            print("未收到响应")
        
        time.sleep(1)  # 等待1秒

def interactive_test():
    """交互式测试"""
    print("\n交互式测试模式")
    print("输入JSON格式的消息，输入'quit'退出")
    print("示例: {\"action\": \"hello\", \"data\": \"测试消息\"}")
    
    while True:
        try:
            user_input = input("\n请输入消息: ").strip()
            if user_input.lower() == 'quit':
                break
                
            message = json.loads(user_input)
            send_test_message(message)
            
            response = read_response()
            if response:
                print(f"响应: {json.dumps(response, indent=2, ensure_ascii=False)}")
            else:
                print("未收到响应")
                
        except json.JSONDecodeError:
            print("JSON格式错误，请重新输入")
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"错误: {e}")

def main():
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        interactive_test()
    else:
        test_basic_communication()

if __name__ == "__main__":
    main()
