#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装和配置Chrome Native Messaging Host
"""

import os
import sys
import json
import shutil
import platform
from pathlib import Path

def get_chrome_native_messaging_dir():
    """获取Chrome Native Messaging配置目录"""
    system = platform.system()
    
    if system == "Windows":
        # Windows注册表路径
        return None  # Windows需要使用注册表
    elif system == "Darwin":  # macOS
        return Path.home() / "Library/Application Support/Google/Chrome/NativeMessagingHosts"
    else:  # Linux
        return Path.home() / ".config/google-chrome/NativeMessagingHosts"

def setup_windows_registry():
    """在Windows上设置注册表"""
    try:
        import winreg
        
        # 获取当前脚本目录
        current_dir = Path(__file__).parent.absolute()
        config_file = current_dir / "com.example.python_host.json"
        
        # 打开注册表键
        key_path = r"SOFTWARE\Google\Chrome\NativeMessagingHosts\com.example.python_host"
        
        try:
            key = winreg.CreateKey(winreg.HKEY_CURRENT_USER, key_path)
            winreg.SetValueEx(key, "", 0, winreg.REG_SZ, str(config_file))
            winreg.CloseKey(key)
            print(f"✓ Windows注册表配置成功: {key_path}")
            return True
        except Exception as e:
            print(f"✗ Windows注册表配置失败: {e}")
            return False
            
    except ImportError:
        print("✗ 无法导入winreg模块")
        return False

def setup_unix_config():
    """在Unix系统上设置配置文件"""
    native_dir = get_chrome_native_messaging_dir()
    if not native_dir:
        return False
        
    try:
        # 创建目录
        native_dir.mkdir(parents=True, exist_ok=True)
        
        # 复制配置文件
        current_dir = Path(__file__).parent.absolute()
        source_config = current_dir / "com.example.python_host.json"
        target_config = native_dir / "com.example.python_host.json"
        
        shutil.copy2(source_config, target_config)
        print(f"✓ 配置文件已复制到: {target_config}")
        return True
        
    except Exception as e:
        print(f"✗ Unix配置失败: {e}")
        return False

def update_config_file():
    """更新配置文件中的路径"""
    try:
        current_dir = Path(__file__).parent.absolute()
        config_file = current_dir / "com.example.python_host.json"
        python_host = current_dir / "python_host.py"
        
        # 读取配置文件
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 更新Python程序路径
        if platform.system() == "Windows":
            # Windows上使用python.exe
            python_exe = sys.executable
            config["path"] = f'"{python_exe}" "{python_host}"'
        else:
            # Unix系统上直接使用脚本路径
            config["path"] = str(python_host)
            # 确保脚本可执行
            os.chmod(python_host, 0o755)
        
        # 写回配置文件
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"✓ 配置文件已更新: {config_file}")
        print(f"  Python路径: {config['path']}")
        return True
        
    except Exception as e:
        print(f"✗ 更新配置文件失败: {e}")
        return False

def main():
    print("Chrome Native Messaging Host 安装程序")
    print("=" * 50)
    
    # 更新配置文件
    if not update_config_file():
        return False
    
    # 根据操作系统进行配置
    system = platform.system()
    print(f"检测到操作系统: {system}")
    
    if system == "Windows":
        success = setup_windows_registry()
    else:
        success = setup_unix_config()
    
    if success:
        print("\n✓ Native Messaging Host 安装成功!")
        print("\n下一步:")
        print("1. 在Chrome中加载chrome-extension文件夹作为扩展")
        print("2. 获取扩展ID并更新com.example.python_host.json中的allowed_origins")
        print("3. 重新运行此安装脚本以应用更新")
        print("4. 测试扩展与Python程序的通信")
    else:
        print("\n✗ 安装失败，请检查错误信息")
    
    return success

if __name__ == "__main__":
    main()
