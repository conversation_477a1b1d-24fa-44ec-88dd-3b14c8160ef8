// Chrome扩展内容脚本 - 可以与页面交互

console.log('Chrome扩展内容脚本已加载');

// 监听来自background script的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('内容脚本收到消息:', message);
  
  switch (message.type) {
    case 'FROM_PYTHON':
      // 可以在这里处理来自Python的消息，比如修改页面内容
      console.log('来自Python的消息:', message.data);
      
      // 示例：在页面上显示来自Python的消息
      if (message.data && message.data.action === 'show_notification') {
        showPageNotification(message.data.message);
      }
      break;
      
    case 'PYTHON_DISCONNECTED':
      console.log('Python连接已断开');
      break;
  }
});

// 在页面上显示通知的函数
function showPageNotification(message) {
  // 创建通知元素
  const notification = document.createElement('div');
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: #4CAF50;
    color: white;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    z-index: 10000;
    font-family: Arial, sans-serif;
    max-width: 300px;
  `;
  notification.textContent = `Python消息: ${message}`;
  
  // 添加到页面
  document.body.appendChild(notification);
  
  // 3秒后自动移除
  setTimeout(() => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification);
    }
  }, 3000);
}

// 示例：向Python发送页面信息
function sendPageInfoToPython() {
  const pageInfo = {
    action: 'page_info',
    url: window.location.href,
    title: document.title,
    timestamp: new Date().toISOString()
  };
  
  chrome.runtime.sendMessage({
    type: 'SEND_TO_PYTHON',
    data: pageInfo
  });
}

// 监听页面加载完成事件
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    console.log('页面加载完成，可以发送页面信息到Python');
    // 可以在这里自动发送页面信息
    // sendPageInfoToPython();
  });
} else {
  console.log('页面已经加载完成');
}
